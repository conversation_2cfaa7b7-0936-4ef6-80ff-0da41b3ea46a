"""LangChain agent for comprehensive business identification."""
import logging
from typing import Dict, Any, Optional, List
import json

from langchain.agents import AgentExecutor, create_react_agent
from langchain_core.prompts import PromptTemplate
from langchain_ollama import OllamaLLM

from config.logging_config import LoggerMixin
from config.settings import get_config
from templates.loader import get_template_loader
from utils.exceptions import handle_exception, LLMError
from .tools import get_business_identification_tools

logger = logging.getLogger(__name__)


class BusinessIdentificationAgent(LoggerMixin):
    """LangChain agent for identifying comprehensive business information."""
    
    def __init__(self, llm: Optional[OllamaLLM] = None):
        """Initialize the business identification agent.
        
        Args:
            llm: Optional LLM instance. If not provided, will create one.
        """
        self.llm = llm
        self.tools = get_business_identification_tools()
        self.template_loader = get_template_loader()
        self.agent_executor = None
        
        self._initialize_agent()
        self.logger.info("BusinessIdentificationAgent initialized")
    
    def _initialize_agent(self):
        """Initialize the Lang<PERSON><PERSON><PERSON> agent with tools and prompt."""
        try:
            # Create LLM if not provided
            if self.llm is None:
                from core.llm_chains import LLMChainFactory
                self.llm = LLMChainFactory.create_llm()
            
            # Load the business identification prompt template
            template_content = self.template_loader.get_business_identification_template()
            
            # Create the agent prompt with ReAct format
            agent_prompt = PromptTemplate.from_template(
                template_content + """

You have access to the following tools:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original question

Begin!

Question: {input}
Thought: {agent_scratchpad}"""
            )
            
            # Create the ReAct agent
            agent = create_react_agent(
                llm=self.llm,
                tools=self.tools,
                prompt=agent_prompt
            )
            
            # Create agent executor
            self.agent_executor = AgentExecutor(
                agent=agent,
                tools=self.tools,
                verbose=True,
                max_iterations=10,
                handle_parsing_errors=True
            )
            
            self.logger.info("Business identification agent initialized successfully")
            
        except Exception as e:
            error = handle_exception(
                self.logger,
                e,
                "Initializing business identification agent"
            )
            raise LLMError(f"Failed to initialize business agent: {e}")
    
    def identify_business(
        self,
        from_field: str,
        subject: str = "",
        body: str = "",
        additional_context: str = ""
    ) -> Dict[str, Any]:
        """Identify comprehensive business information from email data.
        
        Args:
            from_field: Email from field
            subject: Email subject
            body: Email body content
            additional_context: Additional context information
            
        Returns:
            Dictionary containing identified business information
        """
        try:
            self.logger.info(f"Identifying business from email: {from_field}")
            
            # Prepare the input for the agent
            input_data = {
                "from_field": from_field,
                "subject": subject,
                "body": body[:1000] if body else "",  # Limit body length
                "additional_context": additional_context
            }
            
            # Format the question for the agent
            question = f"""
            Identify comprehensive business information from this email:
            
            From: {from_field}
            Subject: {subject}
            Body: {body[:500] if body else 'N/A'}
            Additional Context: {additional_context}
            
            Please identify:
            1. Business/Company Name
            2. Business Website URL
            3. Data Protection Officer (DPO) email address
            4. General contact information (phone, email, address)
            5. Privacy policy URL
            6. Business type/industry
            
            Use the available tools to gather comprehensive information.
            """
            
            # Execute the agent
            result = self.agent_executor.invoke({"input": question})
            
            # Parse and structure the result
            structured_result = self._parse_agent_result(result, input_data)
            
            self.logger.info(f"Business identification completed for: {from_field}")
            return structured_result
            
        except Exception as e:
            error = handle_exception(
                self.logger,
                e,
                f"Identifying business from email: {from_field}"
            )
            
            # Return fallback result
            return self._create_fallback_result(from_field, str(e))
    
    def _parse_agent_result(self, agent_result: Dict[str, Any], input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse and structure the agent result.
        
        Args:
            agent_result: Raw result from agent execution
            input_data: Original input data
            
        Returns:
            Structured business information
        """
        try:
            # Extract the final answer
            final_answer = agent_result.get("output", "")
            
            # Try to extract structured information from the answer
            business_info = {
                "business_name": self._extract_business_name(final_answer, input_data["from_field"]),
                "website_url": self._extract_website_url(final_answer),
                "dpo_email": self._extract_dpo_email(final_answer),
                "contact_info": self._extract_contact_info(final_answer),
                "privacy_policy_url": self._extract_privacy_policy_url(final_answer),
                "business_type": self._extract_business_type(final_answer),
                "source_email": input_data["from_field"],
                "identification_confidence": self._calculate_confidence(final_answer),
                "raw_agent_output": final_answer,
                "tools_used": self._extract_tools_used(agent_result)
            }
            
            return business_info
            
        except Exception as e:
            self.logger.error(f"Error parsing agent result: {e}")
            return self._create_fallback_result(input_data["from_field"], str(e))
    
    def _extract_business_name(self, text: str, from_field: str) -> str:
        """Extract business name from agent output."""
        # Look for business name patterns in the text
        patterns = [
            r"Business[/\s]*Name[:\s]*([^\n\r]+)",
            r"Company[/\s]*Name[:\s]*([^\n\r]+)",
            r"Organization[:\s]*([^\n\r]+)"
        ]
        
        for pattern in patterns:
            import re
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        # Fallback to domain extraction
        if '@' in from_field:
            domain = from_field.split('@')[1]
            return domain.split('.')[0].title()
        
        return "Unknown"
    
    def _extract_website_url(self, text: str) -> Optional[str]:
        """Extract website URL from agent output."""
        import re
        url_pattern = r'https?://[^\s\n\r]+'
        urls = re.findall(url_pattern, text)
        return urls[0] if urls else None
    
    def _extract_dpo_email(self, text: str) -> Optional[str]:
        """Extract DPO email from agent output."""
        import re
        # Look for DPO-specific email patterns
        dpo_patterns = [
            r"DPO[:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})",
            r"Data Protection Officer[:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})",
            r"Privacy Officer[:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})"
        ]
        
        for pattern in dpo_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_contact_info(self, text: str) -> Dict[str, List[str]]:
        """Extract contact information from agent output."""
        import re
        
        contacts = {
            "emails": [],
            "phones": [],
            "addresses": []
        }
        
        # Extract emails
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        contacts["emails"] = list(set(re.findall(email_pattern, text)))
        
        # Extract phone numbers
        phone_patterns = [
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
            r'\b\(\d{3}\)\s*\d{3}[-.]?\d{4}\b'
        ]
        
        for pattern in phone_patterns:
            contacts["phones"].extend(re.findall(pattern, text))
        
        contacts["phones"] = list(set(contacts["phones"]))
        
        return contacts
    
    def _extract_privacy_policy_url(self, text: str) -> Optional[str]:
        """Extract privacy policy URL from agent output."""
        import re
        # Look for privacy policy specific URLs
        privacy_patterns = [
            r"Privacy Policy[:\s]*(https?://[^\s\n\r]+)",
            r"privacy[^\s]*\s*(https?://[^\s\n\r]+)"
        ]
        
        for pattern in privacy_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_business_type(self, text: str) -> Optional[str]:
        """Extract business type from agent output."""
        # Look for business type indicators
        type_patterns = [
            r"Business Type[:\s]*([^\n\r]+)",
            r"Industry[:\s]*([^\n\r]+)",
            r"Sector[:\s]*([^\n\r]+)"
        ]
        
        for pattern in type_patterns:
            import re
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return None
    
    def _calculate_confidence(self, text: str) -> float:
        """Calculate confidence score based on information found."""
        indicators = [
            "business name" in text.lower(),
            "website" in text.lower(),
            "email" in text.lower(),
            "contact" in text.lower(),
            "privacy" in text.lower()
        ]
        
        return sum(indicators) / len(indicators)
    
    def _extract_tools_used(self, agent_result: Dict[str, Any]) -> List[str]:
        """Extract which tools were used by the agent."""
        # This would need to be implemented based on the agent's intermediate steps
        # For now, return the available tools
        return [tool.name for tool in self.tools]
    
    def _create_fallback_result(self, from_field: str, error_msg: str) -> Dict[str, Any]:
        """Create a fallback result when agent execution fails."""
        business_name = "Unknown"
        if '@' in from_field:
            domain = from_field.split('@')[1]
            business_name = domain.split('.')[0].title()
        
        return {
            "business_name": business_name,
            "website_url": None,
            "dpo_email": None,
            "contact_info": {"emails": [], "phones": [], "addresses": []},
            "privacy_policy_url": None,
            "business_type": None,
            "source_email": from_field,
            "identification_confidence": 0.1,
            "error": error_msg,
            "raw_agent_output": f"Agent execution failed: {error_msg}",
            "tools_used": []
        }
