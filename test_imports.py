#!/usr/bin/env python3
"""Test script to verify that all imports work correctly."""

import sys
import traceback

def test_import(module_name, description):
    """Test importing a module and report results."""
    try:
        __import__(module_name)
        print(f"✓ {description}: {module_name}")
        return True
    except ImportError as e:
        print(f"✗ {description}: {module_name} - ImportError: {e}")
        return False
    except Exception as e:
        print(f"✗ {description}: {module_name} - Error: {e}")
        traceback.print_exc()
        return False

def main():
    """Test all critical imports."""
    print("Testing imports...")
    print("=" * 50)
    
    success_count = 0
    total_count = 0
    
    # Test core modules
    tests = [
        ("config.settings", "Configuration settings"),
        ("config.logging_config", "Logging configuration"),
        ("utils.exceptions", "Custom exceptions"),
        ("core.models", "Core data models"),
        ("core.extractors", "Email extractors"),
        ("core.processor", "Email processor"),
        ("core.llm_chains", "LLM chains"),
        ("database.models", "Database models"),
        ("database.manager", "Database manager"),
        ("database.utils", "Database utilities"),
        ("business.manager", "Business manager"),
        ("storage.vector_store", "Vector store"),
        ("queue.manager", "Queue manager"),
        ("queue.jobs", "Queue jobs"),
    ]
    
    for module_name, description in tests:
        total_count += 1
        if test_import(module_name, description):
            success_count += 1
    
    print("=" * 50)
    print(f"Import test results: {success_count}/{total_count} successful")
    
    if success_count == total_count:
        print("✓ All imports successful!")
        return 0
    else:
        print("✗ Some imports failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
