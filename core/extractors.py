"""Email content extraction functionality."""
import re
import os
import logging
from email.parser import Bytes<PERSON>ars<PERSON>
from email.policy import default

from .models import EmailData
from utils.exceptions import ProcessingError, ValidationError, handle_exception
from config.logging_config import LoggerMixin, log_execution_time

logger = logging.getLogger(__name__)

class EmailExtractor(LoggerMixin):
    """Extracts content from email files and anonymizes sensitive information."""
    
    def __init__(self, use_spacy: bool = True):
        """Initialize the email extractor.
        
        Args:
            use_spacy: Whether to use spaCy for NER-based anonymization
        """
        self.use_spacy = use_spacy
        self.nlp = None
        
        if use_spacy:
            try:
                import spacy
                # Load the English model with NER capabilities
                self.logger.info("Loading spaCy model for NER-based anonymization")
                self.nlp = spacy.load("en_core_web_sm")
                self.logger.info("spaCy model loaded successfully")
            except ImportError:
                self.logger.warning("spaCy not installed. Falling back to regex-based anonymization only.")
                self.use_spacy = False
            except OSError:
                self.logger.warning("spaCy model not found. Run 'python -m spacy download en_core_web_sm'")
                self.use_spacy = False
            except Exception as e:
                self.logger.error(f"Unexpected error loading spaCy: {e}")
                self.use_spacy = False
    
    @log_execution_time
    def extract_email_content(self, file_path: str) -> EmailData:
        """Extract content from an email file.
        
        Args:
            file_path: Path to the email file (.eml)
            
        Returns:
            EmailData object containing extracted email content
            
        Raises:
            ProcessingError: If the email file cannot be processed
            ValidationError: If the extracted data is invalid
        """
        self.logger.info(f"Extracting content from email file: {file_path}")
        
        try:
            # Validate file exists
            if not os.path.exists(file_path):
                raise ProcessingError(
                    f"Email file not found: {file_path}",
                    email_path=file_path,
                    stage="file_validation"
                )
            
            # Validate file size (prevent processing extremely large files)
            file_size = os.path.getsize(file_path)
            max_size = 50 * 1024 * 1024  # 50MB
            if file_size > max_size:
                raise ProcessingError(
                    f"Email file too large: {file_size} bytes (max: {max_size})",
                    email_path=file_path,
                    stage="file_validation"
                )
            
            # Parse email file
            try:
                with open(file_path, 'rb') as fp:
                    msg = BytesParser(policy=default).parse(fp)
            except Exception as e:
                raise ProcessingError(
                    f"Failed to parse email file: {str(e)}",
                    email_path=file_path,
                    stage="email_parsing"
                )
            
            # Extract basic headers
            subject = self._clean_header(msg.get('Subject', ''))
            from_field = self._clean_header(msg.get('From', ''))
            to_field = self._clean_header(msg.get('To', ''))
            date_field = self._clean_header(msg.get('Date', ''))
            
            # Extract body content
            body = self._extract_body(msg)
            
            # Validate extracted content
            if not subject and not body:
                raise ValidationError(
                    "Email has no subject or body content",
                    field="content",
                    value=file_path
                )
            
            # Create EmailData object
            try:
                email_data = EmailData(
                    subject=subject,
                    body=body,
                    from_field=from_field,
                    to=to_field,
                    date=date_field
                )
            except Exception as e:
                raise ValidationError(
                    f"Failed to create EmailData object: {str(e)}",
                    field="email_data",
                    value=file_path
                )
            
            self.logger.debug(f"Successfully extracted email content: subject='{subject[:30]}...'")
            return email_data
            
        except (ProcessingError, ValidationError):
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            # Handle unexpected exceptions
            error = handle_exception(self.logger, e, f"Extracting email content from {file_path}")
            raise error
    
    def _clean_header(self, header_value: str) -> str:
        """Clean and decode email header value."""
        if not header_value:
            return ""
        
        try:
            # Decode header if it's encoded
            from email.header import decode_header
            decoded_parts = decode_header(header_value)
            
            cleaned_parts = []
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        part = part.decode(encoding, errors='replace')
                    else:
                        part = part.decode('utf-8', errors='replace')
                cleaned_parts.append(str(part))
            
            return ' '.join(cleaned_parts).strip()
            
        except Exception as e:
            self.logger.warning(f"Error decoding header '{header_value}': {e}")
            return str(header_value).strip()
    
    def _extract_body(self, msg) -> str:
        """Extract body content from email message."""
        body_parts = []
        
        try:
            if msg.is_multipart():
                for part in msg.iter_parts():
                    content_type = part.get_content_type()
                    
                    # Prioritize text/plain, but also accept text/html
                    if content_type in ["text/plain", "text/html"]:
                        try:
                            content = part.get_content()
                            if content:
                                # If it's HTML, we might want to strip tags
                                if content_type == "text/html":
                                    content = self._strip_html_tags(content)
                                body_parts.append(content)
                        except Exception as e:
                            self.logger.warning(f"Error extracting part content: {e}")
            else:
                try:
                    content = msg.get_content()
                    if content:
                        # Check if it's HTML and strip tags if needed
                        content_type = msg.get_content_type()
                        if content_type == "text/html":
                            content = self._strip_html_tags(content)
                        body_parts.append(content)
                except Exception as e:
                    self.logger.warning(f"Error extracting message content: {e}")
            
            # Join all body parts
            body = '\n\n'.join(body_parts).strip()
            
            # Limit body size to prevent memory issues
            max_body_length = 100000  # 100KB
            if len(body) > max_body_length:
                self.logger.warning(f"Body too long ({len(body)} chars), truncating to {max_body_length}")
                body = body[:max_body_length] + "... [TRUNCATED]"
            
            return body
            
        except Exception as e:
            self.logger.error(f"Error extracting email body: {e}")
            return ""
    
    def _strip_html_tags(self, html_content: str) -> str:
        """Strip HTML tags from content."""
        try:
            import re
            # Remove HTML tags
            clean = re.compile('<.*?>')
            text = re.sub(clean, '', html_content)
            
            # Decode HTML entities
            import html
            text = html.unescape(text)
            
            return text.strip()
        except Exception as e:
            self.logger.warning(f"Error stripping HTML tags: {e}")
            return html_content
    
    @log_execution_time
    def anonymize_text(self, text: str) -> str:
        """Anonymize text to remove personally identifiable information.
        
        Uses a combination of regex patterns and spaCy NER (if enabled)
        to identify and replace sensitive information with placeholders.
        
        Args:
            text: The text to anonymize
            
        Returns:
            Anonymized text with PII replaced by placeholders
        """
        if not text:
            return ""
            
        self.logger.debug(f"Anonymizing text (length: {len(text)})")
        
        try:
            # First apply regex-based anonymization
            text = self._apply_regex_anonymization(text)
            
            # Then apply spaCy NER-based anonymization if enabled
            if self.use_spacy and self.nlp and text:
                text = self._apply_ner_anonymization(text)
            
            return text
            
        except Exception as e:
            self.logger.error(f"Error during text anonymization: {e}")
            # Return original text if anonymization fails
            return text
    
    def _apply_regex_anonymization(self, text: str) -> str:
        """Apply regex-based anonymization patterns."""
        try:
            # Replace emails
            text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '[EMAIL]', text)
            
            # Replace phone numbers (various formats)
            phone_patterns = [
                r'\b(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\b',  # US format
                r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # Simple format
                r'\b\(\d{3}\)\s?\d{3}[-.]?\d{4}\b'  # (xxx) xxx-xxxx
            ]
            for pattern in phone_patterns:
                text = re.sub(pattern, '[PHONE]', text)
            
            # Replace URLs
            text = re.sub(r'https?://\S+|www\.\S+', '[URL]', text)
            
            # Replace dates (various formats)
            date_patterns = [
                r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',  # MM/DD/YYYY
                r'\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b',    # YYYY/MM/DD
                r'\b\d{1,2}\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\w*\s+\d{2,4}\b'  # DD Month YYYY
            ]
            for pattern in date_patterns:
                text = re.sub(pattern, '[DATE]', text, flags=re.IGNORECASE)
            
            # Replace addresses (simple pattern)
            text = re.sub(
                r'\b\d+\s+[A-Za-z\s]+(?:Avenue|Ave|Street|St|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr)\b', 
                '[ADDRESS]', 
                text, 
                flags=re.IGNORECASE
            )
            
            # Replace credit card numbers
            text = re.sub(r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b', '[CREDIT_CARD]', text)
            
            # Replace SSN
            text = re.sub(r'\b\d{3}[-.]?\d{2}[-.]?\d{4}\b', '[SSN]', text)
            
            # Replace other long numbers that might be sensitive
            text = re.sub(r'\b\d{6,}\b', '[NUMBER]', text)
            
            return text
            
        except Exception as e:
            self.logger.error(f"Error during regex anonymization: {e}")
            return text
    
    def _apply_ner_anonymization(self, text: str) -> str:
        """Apply spaCy NER-based anonymization."""
        try:
            doc = self.nlp(text)
            
            # Create a list of replacements to make
            replacements = []
            for ent in doc.ents:
                # Define which entity types to anonymize
                if ent.label_ in ["PERSON", "ORG", "GPE", "LOC", "MONEY", "CARDINAL", "DATE", "TIME"]:
                    placeholder = f"[{ent.label_}]"
                    replacements.append((ent.start_char, ent.end_char, placeholder))
            
            # Apply replacements in reverse order to maintain correct indices
            for start, end, placeholder in sorted(replacements, reverse=True):
                text = text[:start] + placeholder + text[end:]
                
            self.logger.debug(f"Applied {len(replacements)} spaCy NER replacements")
            return text
            
        except Exception as e:
            self.logger.error(f"Error during spaCy NER anonymization: {e}")
            return text
