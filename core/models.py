"""Core data models for the email categorization system."""
import logging
from typing import Optional
from datetime import datetime, timezone
from pydantic import BaseModel, validator

from utils.exceptions import ValidationError

logger = logging.getLogger(__name__)

class EmailData(BaseModel):
    """Model representing extracted email data."""
    subject: str
    body: str
    from_field: str = ""
    to: str = ""
    date: str = ""
    
    @validator('subject', 'body')
    def validate_non_empty(cls, v):
        if not v or not v.strip():
            raise ValidationError('Subject and body cannot be empty', field='subject_body', value=v)
        return v.strip()
    
    @validator('from_field', 'to')
    def validate_email_format(cls, v):
        if v and '@' not in v:
            logger.warning(f"Invalid email format: {v}")
        return v
    
    class Config:
        """Pydantic configuration."""
        validate_assignment = True
        str_strip_whitespace = True

class ProcessingResult(BaseModel):
    """Model representing the result of email processing."""
    category: str
    business_entity: str
    template: str
    similarity_score: Optional[float] = None
    matched_with: Optional[str] = None
    status: str
    processing_time: Optional[float] = None
    error_message: Optional[str] = None
    
    @validator('similarity_score')
    def validate_similarity_score(cls, v):
        if v is not None and (v < 0 or v > 1):
            raise ValidationError('Similarity score must be between 0 and 1', field='similarity_score', value=v)
        return v
    
    @validator('category', 'business_entity', 'status')
    def validate_non_empty_strings(cls, v):
        if not v or not v.strip():
            raise ValidationError('Category, business entity, and status cannot be empty')
        return v.strip()
    
    class Config:
        """Pydantic configuration."""
        validate_assignment = True
        str_strip_whitespace = True

class QueueItem(BaseModel):
    """Model representing an item in the Redis queue."""
    user_id: int
    email_path: str
    priority: int = 0
    created_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    
    @validator('user_id')
    def validate_user_id(cls, v):
        if v <= 0:
            raise ValidationError('User ID must be positive', field='user_id', value=v)
        return v
    
    @validator('priority')
    def validate_priority(cls, v):
        if v < 0:
            raise ValidationError('Priority must be non-negative', field='priority', value=v)
        return v
    
    @validator('email_path')
    def validate_email_path(cls, v):
        if not v or not v.strip():
            raise ValidationError('Email path cannot be empty', field='email_path', value=v)
        return v.strip()
    
    @validator('retry_count', 'max_retries')
    def validate_retry_counts(cls, v):
        if v < 0:
            raise ValidationError('Retry counts must be non-negative')
        return v
    
    def __post_init_post_parse__(self):
        """Set created_at if not provided."""
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)
    
    def can_retry(self) -> bool:
        """Check if the item can be retried."""
        return self.retry_count < self.max_retries
    
    def increment_retry(self) -> 'QueueItem':
        """Create a new QueueItem with incremented retry count."""
        return self.copy(update={'retry_count': self.retry_count + 1})
    
    class Config:
        """Pydantic configuration."""
        validate_assignment = True
        str_strip_whitespace = True

class JobResult(BaseModel):
    """Model representing the result of a job execution."""
    job_id: str
    user_id: int
    email_path: str
    status: str  # 'completed', 'failed', 'cancelled'
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    processing_result: Optional[ProcessingResult] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    
    @validator('status')
    def validate_status(cls, v):
        valid_statuses = ['completed', 'failed', 'cancelled', 'in_progress']
        if v not in valid_statuses:
            raise ValidationError(f'Status must be one of {valid_statuses}', field='status', value=v)
        return v
    
    def is_successful(self) -> bool:
        """Check if the job was successful."""
        return self.status == 'completed' and self.processing_result is not None
    
    def get_duration(self) -> Optional[float]:
        """Get job duration in seconds."""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    class Config:
        """Pydantic configuration."""
        validate_assignment = True

class BusinessProfile(BaseModel):
    """Model representing a business profile for API responses."""
    id: int
    name: str
    domain: Optional[str] = None
    industry: Optional[str] = None
    description: Optional[str] = None
    website: Optional[str] = None
    contact_email: Optional[str] = None
    total_interactions: int = 0
    last_interaction: Optional[datetime] = None
    most_common_category: Optional[str] = None
    
    class Config:
        """Pydantic configuration."""
        validate_assignment = True
        orm_mode = True  # Allow creation from SQLAlchemy models

class UserProfile(BaseModel):
    """Model representing a user profile for API responses."""
    id: int
    email: str
    name: Optional[str] = None
    created_at: datetime
    total_emails_processed: int = 0
    business_relationships: int = 0
    
    class Config:
        """Pydantic configuration."""
        validate_assignment = True
        orm_mode = True  # Allow creation from SQLAlchemy models

class SystemHealth(BaseModel):
    """Model representing system health status."""
    status: str  # 'healthy', 'warning', 'unhealthy'
    timestamp: datetime
    database: dict
    queue: dict
    services: dict
    errors: list = []
    
    @validator('status')
    def validate_status(cls, v):
        valid_statuses = ['healthy', 'warning', 'unhealthy']
        if v not in valid_statuses:
            raise ValidationError(f'Status must be one of {valid_statuses}', field='status', value=v)
        return v
    
    class Config:
        """Pydantic configuration."""
        validate_assignment = True
