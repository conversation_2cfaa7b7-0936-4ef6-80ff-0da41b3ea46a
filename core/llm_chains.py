"""LLM chain definitions for email processing."""
import logging
import json
import re
from typing import List, Optional, Dict, Any
from urllib.parse import urlparse

from langchain_ollama import OllamaEmbeddings, OllamaLLM
from langchain_core.prompts import PromptTemplate, FewShotPromptTemplate
from langchain_core.runnables import RunnablePassthrough
from tenacity import retry, stop_after_attempt, wait_exponential

from config.settings import get_config
from config.logging_config import LoggerMixin
from utils.exceptions import LLMError, TemporaryLLMError, handle_exception
from templates.loader import get_template_loader
from agents.tools import WebScrapingTool, DomainLookupTool, BusinessSearchTool, PrivacyPolicyFinderTool

logger = logging.getLogger(__name__)

class LLMChainFactory(LoggerMixin):
    """Factory for creating LLM chains for email processing tasks."""
    
    @staticmethod
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def create_embeddings() -> OllamaEmbeddings:
        """Create Ollama embeddings for vector storage.
        
        Returns:
            OllamaEmbeddings instance configured for text embedding
            
        Raises:
            LLMError: If unable to connect to Ollama service
        """
        logger.info("Creating Ollama embeddings")
        
        try:
            config = get_config()

            embeddings = OllamaEmbeddings(
                base_url=config.ollama.base_url,
                model=config.ollama.embedding_model,
                temperature=0.0,
            )
            
            # Test the embeddings with a simple query
            test_result = embeddings.embed_query("test")
            if not test_result:
                raise LLMError("Embeddings test failed", model=config.ollama.embedding_model, operation="test")
            
            logger.info(f"Ollama embeddings created successfully: {config.ollama.embedding_model}")
            return embeddings
            
        except Exception as e:
            error = handle_exception(logger, e, "Creating Ollama embeddings")
            if isinstance(error, LLMError):
                raise error
            raise LLMError(f"Failed to create Ollama embeddings: {e}", operation="create_embeddings")
    
    @staticmethod
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def create_llm() -> OllamaLLM:
        """Create Ollama LLM for text generation.
        
        Returns:
            OllamaLLM instance configured for text generation
            
        Raises:
            LLMError: If unable to connect to Ollama service
        """
        logger.info("Creating Ollama LLM")
        
        try:
            config = get_config()
            
            llm = OllamaLLM(
                base_url=config.ollama.base_url,
                model=config.ollama.llm_model,
                timeout=config.ollama.timeout,
                temperature=0.1  # Low temperature for consistent results
            )
            
            # Test the LLM with a simple query
            test_result = llm.invoke("Hello")
            if not test_result:
                raise LLMError("LLM test failed", model=config.ollama.llm_model, operation="test")
            
            logger.info(f"Ollama LLM created successfully: {config.ollama.llm_model}")
            return llm
            
        except Exception as e:
            error = handle_exception(logger, e, "Creating Ollama LLM")
            if isinstance(error, LLMError):
                raise error
            raise LLMError(f"Failed to create Ollama LLM: {e}", operation="create_llm")
    
    @staticmethod
    def create_categorization_chain(llm: OllamaLLM, categories: Optional[List[str]] = None) -> Any:
        """Create chain for email categorization.

        Args:
            llm: The LLM to use for categorization
            categories: Optional list of categories to use

        Returns:
            LangChain chain for email categorization

        Raises:
            LLMError: If chain creation fails
        """
        logger.info("Creating enhanced categorization chain")

        try:
            template_loader = get_template_loader()

            if not categories:
                categories = [
                    "business", "social", "marketing", "personal", "shopping",
                    "transaction", "support", "notification"
                ]

            categories_str = ", ".join(categories)

            # Few-shot examples
            examples = [
                {
                    "subject": "Your Amazon order has shipped!",
                    "body": "Thank you for shopping with us. Your order #12345 is on the way.",
                    "category": "transaction"
                },
                {
                    "subject": "Upgrade to Premium and Save 20%",
                    "body": "Take advantage of our limited-time premium plan discount.",
                    "category": "marketing"
                },
                {
                    "subject": "We've received your support request",
                    "body": "Our team is reviewing your issue and will get back shortly.",
                    "category": "support"
                }
            ]

            # Load templates from files
            example_template_content = template_loader.get_categorization_example_template()
            main_template_content = template_loader.get_categorization_template()

            # Prompt template with instructions and structured output
            example_prompt = PromptTemplate(
                input_variables=["subject", "body", "category"],
                template=example_template_content
            )

            prompt = FewShotPromptTemplate(
                examples=examples,
                example_prompt=example_prompt,
                suffix=main_template_content,
                input_variables=["subject", "body", "categories"]
            )

            def categorization_chain(inputs):
                try:
                    formatted_prompt = prompt.format(
                        subject=inputs.get("subject", ""),
                        body=inputs.get("body", "")[:1000],
                        categories=categories_str
                    )
                    result = llm.invoke(formatted_prompt)

                    # Extract JSON category result
                    import json
                    try:
                        category_obj = json.loads(result.strip())
                        category = category_obj.get("category", "other").strip().lower()
                    except Exception:
                        logger.warning("Failed to parse structured response, defaulting to 'other'")
                        category = "other"

                    if category not in [c.lower() for c in categories]:
                        logger.warning(f"Unknown category '{category}', using 'other'")
                        category = "other"

                    return category

                except Exception as e:
                    logger.error(f"Error in categorization chain: {e}")
                    return 'other'

            logger.info("Enhanced categorization chain created successfully")
            return categorization_chain

        except Exception as e:
            error = handle_exception(logger, e, "Creating enhanced categorization chain")
            raise LLMError(f"Failed to create categorization chain: {e}", operation="create_categorization_chain")
    
    @staticmethod
    def create_entity_extraction_chain(llm: OllamaLLM) -> Any:
        """Create chain for business entity extraction.

        Args:
            llm: The LLM to use for entity extraction

        Returns:
            LangChain chain for business entity extraction

        Raises:
            LLMError: If chain creation fails
        """
        logger.info("Creating entity extraction chain")

        try:
            template_loader = get_template_loader()
            template_content = template_loader.get_entity_extraction_template()

            prompt = PromptTemplate(
                input_variables=["from_field", "subject"],
                template=template_content
            )
            
            def entity_extraction_chain(inputs):
                try:
                    formatted_prompt = prompt.format(
                        from_field=inputs.get("from_field", ""),
                        subject=inputs.get("subject", "")
                    )
                    result = llm.invoke(formatted_prompt)
                    
                    # Clean the result
                    entity = result.strip()
                    
                    # If empty or generic, try to extract from email domain
                    if not entity or entity.lower() in ['unknown', 'none', '', 'individual']:
                        from_field = inputs.get("from_field", "")
                        if '@' in from_field:
                            domain = from_field.split('@')[1]
                            entity = domain.split('.')[0].title()
                        else:
                            entity = 'Unknown'
                    
                    return entity
                    
                except Exception as e:
                    logger.error(f"Error in entity extraction chain: {e}")
                    # Fallback to domain extraction
                    from_field = inputs.get("from_field", "")
                    if '@' in from_field:
                        domain = from_field.split('@')[1]
                        return domain.split('.')[0].title()
                    return 'Unknown'
            
            logger.info("Entity extraction chain created successfully")
            return entity_extraction_chain
            
        except Exception as e:
            error = handle_exception(logger, e, "Creating entity extraction chain")
            raise LLMError(f"Failed to create entity extraction chain: {e}", operation="create_entity_extraction_chain")
    
    @staticmethod
    def create_anonymization_chain(llm: OllamaLLM) -> Any:
        """Create chain for email anonymization.

        Args:
            llm: The LLM to use for anonymization

        Returns:
            LangChain chain for email anonymization

        Raises:
            LLMError: If chain creation fails
        """
        logger.info("Creating anonymization chain")

        try:
            template_loader = get_template_loader()
            template_content = template_loader.get_anonymization_template()

            prompt = PromptTemplate(
                input_variables=["subject", "body"],
                template=template_content
            )

            def anonymization_chain(inputs):
                try:
                    formatted_prompt = prompt.format(
                        subject=inputs.get("subject", ""),
                        body=inputs.get("body", "")[:2000]  # Limit body length for LLM
                    )
                    result = llm.invoke(formatted_prompt)

                    # Clean the result
                    template = result.strip()

                    # Ensure we have a reasonable template
                    if len(template) < 10:
                        # Fallback to basic template
                        subject = inputs.get("subject", "")
                        body = inputs.get("body", "")[:500]
                        template = f"Subject: {subject}\n\nBody: {body}"

                    return template

                except Exception as e:
                    logger.error(f"Error in anonymization chain: {e}")
                    # Fallback to basic template
                    subject = inputs.get("subject", "")
                    body = inputs.get("body", "")[:500]
                    return f"Subject: {subject}\n\nBody: {body}"

            logger.info("Anonymization chain created successfully")
            return anonymization_chain

        except Exception as e:
            error = handle_exception(logger, e, "Creating anonymization chain")
            raise LLMError(f"Failed to create anonymization chain: {e}", operation="create_anonymization_chain")

    @staticmethod
    def create_comprehensive_email_analysis_chain(llm: OllamaLLM) -> Any:
        """Create comprehensive chain for email analysis with JSON output.

        Args:
            llm: The LLM to use for analysis

        Returns:
            LangChain chain for comprehensive email analysis

        Raises:
            LLMError: If chain creation fails
        """
        logger.info("Creating comprehensive email analysis chain")

        try:
            # Initialize web search tools
            web_scraper = WebScrapingTool()
            domain_lookup = DomainLookupTool()
            business_search = BusinessSearchTool()
            privacy_finder = PrivacyPolicyFinderTool()

            # Load template
            template_loader = get_template_loader()
            template_content = template_loader.get_comprehensive_analysis_template()

            prompt = PromptTemplate(
                input_variables=["from_field", "subject", "body"],
                template=template_content
            )

            def comprehensive_analysis_chain(inputs):
                try:
                    from_field = inputs.get("from_field", "")
                    subject = inputs.get("subject", "")
                    body = inputs.get("body", "")[:2000]  # Limit body length

                    # First try LLM-based analysis
                    formatted_prompt = prompt.format(
                        from_field=from_field,
                        subject=subject,
                        body=body
                    )

                    llm_result = llm.invoke(formatted_prompt)

                    # Try to parse JSON response
                    try:
                        result = json.loads(llm_result.strip())

                        # Validate required fields
                        required_fields = ["email_category", "business_entity_name", "business_entity_email",
                                         "business_entity_website", "business_entity_dpo_email"]

                        if all(field in result for field in required_fields):
                            # Enhance with web search if information is missing
                            if not result.get("business_entity_email") or not result.get("business_entity_website") or not result.get("business_entity_dpo_email"):
                                domain = LLMChainFactory._extract_domain_from_email(from_field)
                                business_info = LLMChainFactory._search_business_information(
                                    result["business_entity_name"], domain, web_scraper, domain_lookup, business_search, privacy_finder
                                )

                                # Fill in missing information
                                if not result.get("business_entity_email"):
                                    result["business_entity_email"] = business_info.get("email", "")
                                if not result.get("business_entity_website"):
                                    result["business_entity_website"] = business_info.get("website", "")
                                if not result.get("business_entity_dpo_email"):
                                    result["business_entity_dpo_email"] = business_info.get("dpo_email", "")

                            logger.info(f"Comprehensive analysis completed: {result}")
                            return result

                    except json.JSONDecodeError:
                        logger.warning("Failed to parse LLM JSON response, falling back to manual extraction")

                    # Fallback: Manual extraction + web search
                    category = LLMChainFactory._categorize_email_internal(llm, subject, body)
                    business_name = LLMChainFactory._extract_business_name_internal(llm, from_field, subject)
                    domain = LLMChainFactory._extract_domain_from_email(from_field)

                    business_info = LLMChainFactory._search_business_information(
                        business_name, domain, web_scraper, domain_lookup, business_search, privacy_finder
                    )

                    result = {
                        "email_category": category,
                        "business_entity_name": business_name,
                        "business_entity_email": business_info.get("email", ""),
                        "business_entity_website": business_info.get("website", ""),
                        "business_entity_dpo_email": business_info.get("dpo_email", "")
                    }

                    logger.info(f"Comprehensive analysis completed (fallback): {result}")
                    return result

                except Exception as e:
                    logger.error(f"Error in comprehensive analysis chain: {e}")
                    # Return fallback response
                    domain = LLMChainFactory._extract_domain_from_email(inputs.get("from_field", ""))
                    return {
                        "email_category": "other",
                        "business_entity_name": domain.split('.')[0].title() if domain else "Unknown",
                        "business_entity_email": "",
                        "business_entity_website": f"https://{domain}" if domain else "",
                        "business_entity_dpo_email": ""
                    }

            logger.info("Comprehensive email analysis chain created successfully")
            return comprehensive_analysis_chain

        except Exception as e:
            error = handle_exception(logger, e, "Creating comprehensive email analysis chain")
            raise LLMError(f"Failed to create comprehensive email analysis chain: {e}", operation="create_comprehensive_analysis_chain")

    @staticmethod
    def _categorize_email_internal(llm: OllamaLLM, subject: str, body: str) -> str:
        """Internal method to categorize email."""
        try:
            categories = ["business", "social", "marketing", "personal", "shopping", "transaction", "support", "notification"]
            categories_str = ", ".join(categories)

            prompt_text = f"""
Email:
Subject: {subject}
Body: {body[:1000]}

Instructions:
- Choose the most appropriate category from this list: {categories_str}.
- Return only the category name in lowercase.
- Use:
    - "marketing" for promotional/advertising content
    - "transaction" for order confirmations, invoices, or receipts
    - "support" for customer service or helpdesk replies
    - "notification" for alerts or system-generated messages
- If unsure, select the closest possible match

Category:"""

            result = llm.invoke(prompt_text)
            category = result.strip().lower()

            if category not in categories:
                category = "other"

            return category

        except Exception as e:
            logger.error(f"Error in internal categorization: {e}")
            return "other"

    @staticmethod
    def _extract_business_name_internal(llm: OllamaLLM, from_field: str, subject: str) -> str:
        """Internal method to extract business name."""
        try:
            prompt_text = f"""
Extract the business entity or organization name from this email information:

From: {from_field}
Subject: {subject}

Instructions:
- Identify the business, company, or organization name
- Return only the clean business name without email domains
- If it's from an individual person, return "Individual"
- If it's a system/automated email, extract the service name
- Remove common suffixes like "Inc", "LLC", "Corp" for consistency
- If unclear, extract the domain name without extension

Business Name:"""

            result = llm.invoke(prompt_text)
            entity = result.strip()

            # If empty or generic, try to extract from email domain
            if not entity or entity.lower() in ['unknown', 'none', '', 'individual']:
                if '@' in from_field:
                    domain = from_field.split('@')[1]
                    entity = domain.split('.')[0].title()
                else:
                    entity = 'Unknown'

            return entity

        except Exception as e:
            logger.error(f"Error in internal business name extraction: {e}")
            # Fallback to domain extraction
            if '@' in from_field:
                domain = from_field.split('@')[1]
                return domain.split('.')[0].title()
            return 'Unknown'

    @staticmethod
    def _extract_domain_from_email(email: str) -> str:
        """Extract domain from email address."""
        try:
            if '@' in email:
                return email.split('@')[1]
            return ""
        except Exception:
            return ""

    @staticmethod
    def _search_business_information(
        business_name: str,
        domain: str,
        web_scraper: WebScrapingTool,
        domain_lookup: DomainLookupTool,
        business_search: BusinessSearchTool,
        privacy_finder: PrivacyPolicyFinderTool
    ) -> Dict[str, str]:
        """Search for business information using web tools."""
        try:
            logger.info(f"Searching business information for: {business_name}, domain: {domain}")

            result = {
                "email": "",
                "website": "",
                "dpo_email": ""
            }

            # Step 1: Try to get website from domain
            if domain:
                website_url = f"https://{domain}"
                result["website"] = website_url

                # Step 2: Scrape the website for contact information
                try:
                    scrape_result = web_scraper._run(website_url, extract_contacts=True)

                    # Safely parse the result
                    scrape_data = None
                    if isinstance(scrape_result, str):
                        try:
                            scrape_data = json.loads(scrape_result)
                        except json.JSONDecodeError:
                            # Try to extract from string representation
                            import ast
                            try:
                                scrape_data = ast.literal_eval(scrape_result)
                            except (ValueError, SyntaxError):
                                logger.warning(f"Could not parse scrape result: {scrape_result[:100]}...")
                    else:
                        scrape_data = scrape_result

                    if isinstance(scrape_data, dict):
                        contacts = scrape_data.get('contacts', {})
                        if isinstance(contacts, dict):
                            # Extract general business email
                            emails = contacts.get('emails', [])
                            if emails and isinstance(emails, list):
                                # Prefer contact, info, or support emails
                                for email in emails:
                                    if any(keyword in email.lower() for keyword in ['contact', 'info', 'support', 'hello']):
                                        result["email"] = email
                                        break
                                if not result["email"] and emails:
                                    result["email"] = emails[0]

                except Exception as e:
                    logger.warning(f"Error scraping website {website_url}: {e}")

                # Step 3: Look for privacy policy and DPO information
                try:
                    privacy_result = privacy_finder._run(website_url, extract_contacts=True)

                    # Safely parse the result
                    privacy_data = None
                    if isinstance(privacy_result, str):
                        try:
                            privacy_data = json.loads(privacy_result)
                        except json.JSONDecodeError:
                            # Try to extract from string representation
                            import ast
                            try:
                                privacy_data = ast.literal_eval(privacy_result)
                            except (ValueError, SyntaxError):
                                logger.warning(f"Could not parse privacy result: {privacy_result[:100]}...")
                    else:
                        privacy_data = privacy_result

                    if isinstance(privacy_data, dict):
                        dpo_contacts = privacy_data.get('dpo_contacts', [])
                        if dpo_contacts and isinstance(dpo_contacts, list):
                            result["dpo_email"] = dpo_contacts[0]
                        elif not result["dpo_email"]:
                            # Try privacy contacts as fallback
                            privacy_contacts = privacy_data.get('privacy_contacts', [])
                            if privacy_contacts and isinstance(privacy_contacts, list):
                                result["dpo_email"] = privacy_contacts[0]

                except Exception as e:
                    logger.warning(f"Error finding privacy policy for {website_url}: {e}")

            # Step 4: If no website found, try business search
            if not result["website"] and business_name:
                try:
                    search_result = business_search._run(business_name)
                    # This is a placeholder - in production, this would use actual search APIs
                    logger.info(f"Business search result: {search_result}")
                except Exception as e:
                    logger.warning(f"Error in business search: {e}")

            # Step 5: Domain lookup for additional information
            if domain:
                try:
                    domain_result = domain_lookup._run(domain)
                    logger.info(f"Domain lookup result: {domain_result}")
                except Exception as e:
                    logger.warning(f"Error in domain lookup: {e}")

            logger.info(f"Business information search completed: {result}")
            return result

        except Exception as e:
            logger.error(f"Error searching business information: {e}")
            return {
                "email": "",
                "website": f"https://{domain}" if domain else "",
                "dpo_email": ""
            }
