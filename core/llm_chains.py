"""LLM chain definitions for email processing."""
import logging
from typing import List, Optional, Dict, Any

from langchain_ollama import OllamaEmbeddings, OllamaLLM
from langchain_core.prompts import PromptTemplate, FewShotPromptTemplate
from langchain_core.runnables import RunnablePassthrough
from tenacity import retry, stop_after_attempt, wait_exponential

from config.settings import get_config
from config.logging_config import LoggerMixin
from utils.exceptions import LLMError, TemporaryLLMError, handle_exception
from templates.loader import get_template_loader

logger = logging.getLogger(__name__)

class LLMChainFactory(LoggerMixin):
    """Factory for creating LLM chains for email processing tasks."""
    
    @staticmethod
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def create_embeddings() -> OllamaEmbeddings:
        """Create Ollama embeddings for vector storage.
        
        Returns:
            OllamaEmbeddings instance configured for text embedding
            
        Raises:
            LLMError: If unable to connect to Ollama service
        """
        logger.info("Creating Ollama embeddings")
        
        try:
            config = get_config()

            embeddings = OllamaEmbeddings(
                base_url=config.ollama.base_url,
                model=config.ollama.embedding_model,
                temperature=0.0,
            )
            
            # Test the embeddings with a simple query
            test_result = embeddings.embed_query("test")
            if not test_result:
                raise LLMError("Embeddings test failed", model=config.ollama.embedding_model, operation="test")
            
            logger.info(f"Ollama embeddings created successfully: {config.ollama.embedding_model}")
            return embeddings
            
        except Exception as e:
            error = handle_exception(logger, e, "Creating Ollama embeddings")
            if isinstance(error, LLMError):
                raise error
            raise LLMError(f"Failed to create Ollama embeddings: {e}", operation="create_embeddings")
    
    @staticmethod
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def create_llm() -> OllamaLLM:
        """Create Ollama LLM for text generation.
        
        Returns:
            OllamaLLM instance configured for text generation
            
        Raises:
            LLMError: If unable to connect to Ollama service
        """
        logger.info("Creating Ollama LLM")
        
        try:
            config = get_config()
            
            llm = OllamaLLM(
                base_url=config.ollama.base_url,
                model=config.ollama.llm_model,
                timeout=config.ollama.timeout,
                temperature=0.1  # Low temperature for consistent results
            )
            
            # Test the LLM with a simple query
            test_result = llm.invoke("Hello")
            if not test_result:
                raise LLMError("LLM test failed", model=config.ollama.llm_model, operation="test")
            
            logger.info(f"Ollama LLM created successfully: {config.ollama.llm_model}")
            return llm
            
        except Exception as e:
            error = handle_exception(logger, e, "Creating Ollama LLM")
            if isinstance(error, LLMError):
                raise error
            raise LLMError(f"Failed to create Ollama LLM: {e}", operation="create_llm")
    
    @staticmethod
    def create_categorization_chain(llm: OllamaLLM, categories: Optional[List[str]] = None) -> Any:
        """Create chain for email categorization.

        Args:
            llm: The LLM to use for categorization
            categories: Optional list of categories to use

        Returns:
            LangChain chain for email categorization

        Raises:
            LLMError: If chain creation fails
        """
        logger.info("Creating enhanced categorization chain")

        try:
            template_loader = get_template_loader()

            if not categories:
                categories = [
                    "business", "social", "marketing", "personal", "shopping",
                    "transaction", "support", "notification"
                ]

            categories_str = ", ".join(categories)

            # Few-shot examples
            examples = [
                {
                    "subject": "Your Amazon order has shipped!",
                    "body": "Thank you for shopping with us. Your order #12345 is on the way.",
                    "category": "transaction"
                },
                {
                    "subject": "Upgrade to Premium and Save 20%",
                    "body": "Take advantage of our limited-time premium plan discount.",
                    "category": "marketing"
                },
                {
                    "subject": "We've received your support request",
                    "body": "Our team is reviewing your issue and will get back shortly.",
                    "category": "support"
                }
            ]

            # Load templates from files
            example_template_content = template_loader.get_categorization_example_template()
            main_template_content = template_loader.get_categorization_template()

            # Prompt template with instructions and structured output
            example_prompt = PromptTemplate(
                input_variables=["subject", "body", "category"],
                template=example_template_content
            )

            prompt = FewShotPromptTemplate(
                examples=examples,
                example_prompt=example_prompt,
                suffix=main_template_content,
                input_variables=["subject", "body", "categories"]
            )

            def categorization_chain(inputs):
                try:
                    formatted_prompt = prompt.format(
                        subject=inputs.get("subject", ""),
                        body=inputs.get("body", "")[:1000],
                        categories=categories_str
                    )
                    result = llm.invoke(formatted_prompt)

                    # Extract JSON category result
                    import json
                    try:
                        category_obj = json.loads(result.strip())
                        category = category_obj.get("category", "other").strip().lower()
                    except Exception:
                        logger.warning("Failed to parse structured response, defaulting to 'other'")
                        category = "other"

                    if category not in [c.lower() for c in categories]:
                        logger.warning(f"Unknown category '{category}', using 'other'")
                        category = "other"

                    return category

                except Exception as e:
                    logger.error(f"Error in categorization chain: {e}")
                    return 'other'

            logger.info("Enhanced categorization chain created successfully")
            return categorization_chain

        except Exception as e:
            error = handle_exception(logger, e, "Creating enhanced categorization chain")
            raise LLMError(f"Failed to create categorization chain: {e}", operation="create_categorization_chain")
    
    @staticmethod
    def create_entity_extraction_chain(llm: OllamaLLM) -> Any:
        """Create chain for business entity extraction.

        Args:
            llm: The LLM to use for entity extraction

        Returns:
            LangChain chain for business entity extraction

        Raises:
            LLMError: If chain creation fails
        """
        logger.info("Creating entity extraction chain")

        try:
            template_loader = get_template_loader()
            template_content = template_loader.get_entity_extraction_template()

            prompt = PromptTemplate(
                input_variables=["from_field", "subject"],
                template=template_content
            )
            
            def entity_extraction_chain(inputs):
                try:
                    formatted_prompt = prompt.format(
                        from_field=inputs.get("from_field", ""),
                        subject=inputs.get("subject", "")
                    )
                    result = llm.invoke(formatted_prompt)
                    
                    # Clean the result
                    entity = result.strip()
                    
                    # If empty or generic, try to extract from email domain
                    if not entity or entity.lower() in ['unknown', 'none', '', 'individual']:
                        from_field = inputs.get("from_field", "")
                        if '@' in from_field:
                            domain = from_field.split('@')[1]
                            entity = domain.split('.')[0].title()
                        else:
                            entity = 'Unknown'
                    
                    return entity
                    
                except Exception as e:
                    logger.error(f"Error in entity extraction chain: {e}")
                    # Fallback to domain extraction
                    from_field = inputs.get("from_field", "")
                    if '@' in from_field:
                        domain = from_field.split('@')[1]
                        return domain.split('.')[0].title()
                    return 'Unknown'
            
            logger.info("Entity extraction chain created successfully")
            return entity_extraction_chain
            
        except Exception as e:
            error = handle_exception(logger, e, "Creating entity extraction chain")
            raise LLMError(f"Failed to create entity extraction chain: {e}", operation="create_entity_extraction_chain")
    
    @staticmethod
    def create_anonymization_chain(llm: OllamaLLM) -> Any:
        """Create chain for email anonymization.

        Args:
            llm: The LLM to use for anonymization

        Returns:
            LangChain chain for email anonymization

        Raises:
            LLMError: If chain creation fails
        """
        logger.info("Creating anonymization chain")

        try:
            template_loader = get_template_loader()
            template_content = template_loader.get_anonymization_template()

            prompt = PromptTemplate(
                input_variables=["subject", "body"],
                template=template_content
            )
            
            def anonymization_chain(inputs):
                try:
                    formatted_prompt = prompt.format(
                        subject=inputs.get("subject", ""),
                        body=inputs.get("body", "")[:2000]  # Limit body length for LLM
                    )
                    result = llm.invoke(formatted_prompt)
                    
                    # Clean the result
                    template = result.strip()
                    
                    # Ensure we have a reasonable template
                    if len(template) < 10:
                        # Fallback to basic template
                        subject = inputs.get("subject", "")
                        body = inputs.get("body", "")[:500]
                        template = f"Subject: {subject}\n\nBody: {body}"
                    
                    return template
                    
                except Exception as e:
                    logger.error(f"Error in anonymization chain: {e}")
                    # Fallback to basic template
                    subject = inputs.get("subject", "")
                    body = inputs.get("body", "")[:500]
                    return f"Subject: {subject}\n\nBody: {body}"
            
            logger.info("Anonymization chain created successfully")
            return anonymization_chain
            
        except Exception as e:
            error = handle_exception(logger, e, "Creating anonymization chain")
            raise LLMError(f"Failed to create anonymization chain: {e}", operation="create_anonymization_chain")
