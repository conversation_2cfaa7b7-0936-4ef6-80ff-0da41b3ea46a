"""Core email processing functionality."""
import os
import hashlib
import logging
import time
from typing import Dict, List, Any, Optional

from core.models import EmailData, ProcessingResult
from core.extractors import EmailExtractor
from core.llm_chains import LLMChainFactory
from storage.vector_store import VectorStore
from config.settings import get_config
from config.logging_config import LoggerMixin, log_execution_time
from database.utils import ensure_database_ready
from database.manager import get_db_manager
from business.manager import get_business_manager
from utils.exceptions import ProcessingError, handle_exception

logger = logging.getLogger(__name__)

class EmailProcessor(LoggerMixin):
    """Processes emails for categorization and template extraction."""
    
    def __init__(
        self,
        user_email: str = None,
        email_folder: str = None,
        template_output_dir: str = None,
        categories: Optional[List[str]] = None,
        use_spacy: bool = True,
        config_override: dict = None
    ):
        """Initialize the email processor.

        Args:
            user_email: Email address of the user (for tracking interactions)
            email_folder: Path to folder containing email files (uses config if not provided)
            template_output_dir: Directory to save processed templates (uses config if not provided)
            categories: Optional list of categories for classification (uses config if not provided)
            use_spacy: Whether to use spaCy for NER-based anonymization
            config_override: Optional configuration overrides

        Raises:
            ProcessingError: If initialization fails
        """
        try:
            # Load configuration
            self.config = get_config()
            if config_override:
                # Apply any configuration overrides
                for key, value in config_override.items():
                    if hasattr(self.config, key):
                        setattr(self.config, key, value)

            # Set parameters from config if not provided
            self.email_folder = email_folder or self.config.directories.email_folder
            self.template_output_dir = template_output_dir or self.config.directories.template_output_dir
            self.categories = categories or self.config.processing.default_categories
            self.user_email = user_email

            self.logger.info("Initializing EmailProcessor", extra={
                'email_folder': self.email_folder,
                'chromadb_host': self.config.chromadb.host,
                'chromadb_port': self.config.chromadb.port,
                'user_email': self.user_email,
                'environment': self.config.environment
            })

            # Ensure database is ready
            ensure_database_ready()
            
            # Initialize database and business manager
            self.db = get_db_manager()
            self.business_manager = get_business_manager()

            # Get or create user if email provided
            self.user = None
            if self.user_email:
                self.user = self.db.get_or_create_user(self.user_email)
                self.logger.info("User initialized", extra={
                    'user_email': self.user.email,
                    'user_id': self.user.id
                })

            # Ensure directories exist
            self._ensure_directories()

            # Initialize LLM components
            self._initialize_llm_components(use_spacy)

            self.logger.info("EmailProcessor initialized successfully")
            
        except Exception as e:
            error = handle_exception(self.logger, e, "Initializing EmailProcessor")
            raise ProcessingError(f"Failed to initialize EmailProcessor: {error}", stage="initialization")
    
    def _ensure_directories(self):
        """Ensure required directories exist."""
        try:
            os.makedirs(self.email_folder, exist_ok=True)
            os.makedirs(self.template_output_dir, exist_ok=True)
            self.logger.info(f"Created output directories: {self.email_folder}, {self.template_output_dir}")
        except Exception as e:
            raise ProcessingError(f"Failed to create required directories: {e}", stage="directory_creation")
    
    def _initialize_llm_components(self, use_spacy: bool):
        """Initialize LLM and related components."""
        try:
            # Initialize LLM components
            self.embeddings = LLMChainFactory.create_embeddings()
            self.llm = LLMChainFactory.create_llm()

            # Initialize chains
            self.categorization_chain = LLMChainFactory.create_categorization_chain(self.llm, self.categories)
            self.entity_extraction_chain = LLMChainFactory.create_entity_extraction_chain(self.llm)
            self.anonymization_chain = LLMChainFactory.create_anonymization_chain(self.llm)
            self.comprehensive_analysis_chain = LLMChainFactory.create_comprehensive_email_analysis_chain(self.llm)

            # Initialize vector store
            self.vector_store = VectorStore(
                embeddings=self.embeddings,
                chromadb_host=self.config.chromadb.host,
                chromadb_port=self.config.chromadb.port
            )

            # Initialize extractor
            self.extractor = EmailExtractor(use_spacy=use_spacy)
            
            self.logger.info("LLM components initialized successfully")
            
        except Exception as e:
            raise ProcessingError(f"Failed to initialize LLM components: {e}", stage="llm_initialization")
    
    @log_execution_time
    def create_anonymized_template(self, email_data: EmailData) -> str:
        """Create an anonymized template from email data using LLM.
        
        Args:
            email_data: The email data to anonymize
            
        Returns:
            Anonymized template string
            
        Raises:
            ProcessingError: If anonymization fails
        """
        self.logger.debug("Creating anonymized template")
        
        try:
            # First apply basic regex and NER anonymization
            subject = self.extractor.anonymize_text(email_data.subject)
            body = self.extractor.anonymize_text(email_data.body[:1000])  # Limit body length
            
            self.logger.debug("Basic anonymization complete", extra={
                'subject_preview': subject[:30] + '...' if len(subject) > 30 else subject,
                'body_length': len(body)
            })

            # Then use LLM for more sophisticated anonymization
            try:
                result = self.anonymization_chain({
                    "subject": subject,
                    "body": body
                })
                self.logger.debug("LLM anonymization successful", extra={
                    'result_length': len(result.strip())
                })
                return result.strip()
            except Exception as e:
                self.logger.warning("Error during LLM anonymization", extra={
                    'error': str(e),
                    'error_type': type(e).__name__
                })
                # Fallback to basic anonymization if LLM fails
                self.logger.info("Falling back to basic anonymization")
                return f"Subject: {subject}\n\nBody: {body[:500]}"
                
        except Exception as e:
            raise ProcessingError(f"Failed to create anonymized template: {e}", stage="anonymization")
    
    @log_execution_time
    def categorize_email(self, email_data: EmailData) -> str:
        """Categorize email using LangChain LLM chain.
        
        Args:
            email_data: The email data to categorize
            
        Returns:
            Category string
            
        Raises:
            ProcessingError: If categorization fails
        """
        self.logger.debug("Categorizing email")
        
        try:
            result = self.categorization_chain({
                "subject": email_data.subject,
                "body": email_data.body[:1000]  # Limit body length
            })
            category = result.strip().lower()
            
            # Validate category against known categories
            if category not in self.categories:
                self.logger.warning(f"Unknown category '{category}', defaulting to 'other'")
                category = 'other'
            
            self.logger.info("Email categorized", extra={
                'category': category,
                'subject_preview': email_data.subject[:50] + '...' if len(email_data.subject) > 50 else email_data.subject
            })
            return category

        except Exception as e:
            self.logger.error("Failed to categorize email", extra={
                'error': str(e),
                'error_type': type(e).__name__,
                'subject_preview': email_data.subject[:50] + '...' if len(email_data.subject) > 50 else email_data.subject
            })
            # Return default category instead of failing
            return 'other'
    
    @log_execution_time
    def extract_business_entity(self, email_data: EmailData) -> str:
        """Extract business entity using LangChain LLM chain.
        
        Args:
            email_data: The email data to extract entity from
            
        Returns:
            Business entity string
            
        Raises:
            ProcessingError: If entity extraction fails
        """
        self.logger.debug("Extracting business entity")
        
        try:
            result = self.entity_extraction_chain({
                "from_field": email_data.from_field,
                "subject": email_data.subject
            })
            entity = result.strip()
            
            # If no entity found, try to extract from email domain
            if not entity or entity.lower() in ['unknown', 'none', '']:
                if '@' in email_data.from_field:
                    domain = email_data.from_field.split('@')[1]
                    entity = domain.split('.')[0].title()
            
            self.logger.info("Extracted business entity", extra={
                'entity': entity,
                'from_field': email_data.from_field,
                'subject_preview': email_data.subject[:50] + '...' if len(email_data.subject) > 50 else email_data.subject
            })
            return entity

        except Exception as e:
            self.logger.warning("Failed to extract business entity", extra={
                'error': str(e),
                'error_type': type(e).__name__,
                'from_field': email_data.from_field
            })
            # Return fallback entity
            if '@' in email_data.from_field:
                domain = email_data.from_field.split('@')[1]
                return domain.split('.')[0].title()
            return 'Unknown'

    @log_execution_time
    def analyze_email_comprehensive(self, email_data: EmailData) -> Dict[str, str]:
        """Perform comprehensive email analysis with JSON output.

        Args:
            email_data: The email data to analyze

        Returns:
            Dictionary with comprehensive analysis results

        Raises:
            ProcessingError: If analysis fails
        """
        self.logger.debug("Performing comprehensive email analysis")

        try:
            result = self.comprehensive_analysis_chain({
                "from_field": email_data.from_field,
                "subject": email_data.subject,
                "body": email_data.body
            })

            self.logger.info("Comprehensive email analysis completed", extra={
                'category': result.get('email_category'),
                'business_name': result.get('business_entity_name'),
                'has_business_email': bool(result.get('business_entity_email')),
                'has_website': bool(result.get('business_entity_website')),
                'has_dpo_email': bool(result.get('business_entity_dpo_email'))
            })

            return result

        except Exception as e:
            self.logger.error("Failed to perform comprehensive email analysis", extra={
                'error': str(e),
                'error_type': type(e).__name__,
                'from_field': email_data.from_field,
                'subject_preview': email_data.subject[:50] + '...' if len(email_data.subject) > 50 else email_data.subject
            })
            # Return fallback response
            domain = email_data.from_field.split('@')[1] if '@' in email_data.from_field else ""
            return {
                "email_category": "other",
                "business_entity_name": domain.split('.')[0].title() if domain else "Unknown",
                "business_entity_email": "",
                "business_entity_website": f"https://{domain}" if domain else "",
                "business_entity_dpo_email": ""
            }

    @log_execution_time
    def process_email_comprehensive(self, file_path: str) -> Dict[str, Any]:
        """Process a single email file with comprehensive analysis.

        Args:
            file_path: Path to the email file

        Returns:
            Dictionary containing comprehensive analysis results
        """
        start_time = time.time()
        self.logger.info("Processing email with comprehensive analysis", extra={
            'file_path': file_path,
            'user_email': self.user_email,
            'user_id': self.user.id if self.user else None
        })

        try:
            # Extract email content
            email_data = self.extractor.extract_email_content(file_path)

            # Perform comprehensive analysis
            analysis_result = self.analyze_email_comprehensive(email_data)

            # Create an anonymized template from the email
            anonymized_template = self.create_anonymized_template(email_data)

            # Generate ID for the document
            doc_id = hashlib.md5(anonymized_template.encode()).hexdigest()

            # Check if the document ID already exists in the collection
            is_duplicate = self.vector_store.check_duplicate(doc_id)

            # Check for similar documents
            is_similar, similar_metadata, similarity_score = self.vector_store.find_similar(anonymized_template)

            # Record business interaction if user is provided
            business_interaction_id = None
            if self.user and analysis_result.get("business_entity_name"):
                try:
                    business_profile = self.business_manager.get_or_create_business_entity(
                        analysis_result["business_entity_name"], email_data
                    )
                    interaction = self.business_manager.record_user_business_interaction(
                        user=self.user,
                        business_entity=business_profile,
                        email_data=email_data,
                        category=analysis_result["email_category"],
                        email_file_path=file_path,
                        template_id=similar_metadata.get('template_id') if similar_metadata else doc_id,
                        similarity_score=similarity_score
                    )
                    business_interaction_id = interaction.id if interaction else None
                except Exception as e:
                    self.logger.warning(f"Failed to record business interaction: {e}")

            # Store in vector database if not duplicate and not similar
            if not is_duplicate and not is_similar:
                try:
                    self.vector_store.add_document(
                        content=anonymized_template,
                        metadata={
                            'template_id': doc_id,
                            'category': analysis_result["email_category"],
                            'business_entity': analysis_result["business_entity_name"],
                            'file_path': file_path,
                            'user_email': self.user_email,
                            'user_id': self.user.id if self.user else None,
                            'business_interaction_id': business_interaction_id
                        },
                        doc_id=doc_id
                    )
                    storage_status = "Stored as new template"
                except Exception as e:
                    self.logger.error(f"Failed to store document in vector database: {e}")
                    storage_status = f"Storage failed: {e}"
            elif is_duplicate:
                storage_status = "Duplicate (exact match found)"
            else:
                storage_status = f"Similar template found (similarity: {similarity_score:.2f})"

            processing_time = time.time() - start_time

            # Combine analysis result with processing metadata
            result = {
                **analysis_result,
                "template": anonymized_template,
                "status": storage_status,
                "processing_time": processing_time,
                "similarity_score": similarity_score,
                "is_duplicate": is_duplicate,
                "is_similar": is_similar,
                "business_interaction_id": business_interaction_id,
                "document_id": doc_id
            }

            self.logger.info("Email processing completed", extra={
                'file_path': file_path,
                'category': analysis_result["email_category"],
                'business_entity': analysis_result["business_entity_name"],
                'status': storage_status,
                'processing_time': processing_time
            })

            return result

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Failed to process email {file_path}: {e}"
            self.logger.error(error_msg, extra={
                'file_path': file_path,
                'error': str(e),
                'error_type': type(e).__name__,
                'processing_time': processing_time
            })

            # Return error response
            return {
                "email_category": "error",
                "business_entity_name": "Unknown",
                "business_entity_email": "",
                "business_entity_website": "",
                "business_entity_dpo_email": "",
                "template": "",
                "status": f"Processing failed: {e}",
                "processing_time": processing_time,
                "similarity_score": 0.0,
                "is_duplicate": False,
                "is_similar": False,
                "business_interaction_id": None,
                "document_id": ""
            }

    @log_execution_time
    def process_email(self, file_path: str) -> ProcessingResult:
        """Process a single email file.
        
        Args:
            file_path: Path to the email file
            
        Returns:
            ProcessingResult object with processing results
        """
        start_time = time.time()
        self.logger.info("Processing email", extra={
            'file_path': file_path,
            'user_email': self.user_email,
            'user_id': self.user.id if self.user else None
        })
        
        try:
            # Extract email content
            email_data = self.extractor.extract_email_content(file_path)
            
            # Categorize email
            category = self.categorize_email(email_data)
            
            # Extract business entity
            business_entity = self.extract_business_entity(email_data)
            
            # Create an anonymized template from the email
            anonymized_template = self.create_anonymized_template(email_data)
            
            # Generate ID for the document
            doc_id = hashlib.md5(anonymized_template.encode()).hexdigest()
            
            # Check if the document ID already exists in the collection
            if self.vector_store.check_duplicate(doc_id):
                processing_time = time.time() - start_time
                return ProcessingResult(
                    category=category,
                    business_entity=business_entity,
                    template=anonymized_template,
                    status="Duplicate (exact match found)",
                    processing_time=processing_time
                )
            
            # Check for similar documents
            is_similar, similar_metadata, similarity_score = self.vector_store.find_similar(anonymized_template)

            # Record business interaction regardless of similarity
            if self.user and business_entity and business_entity.strip():
                try:
                    business_profile = self.business_manager.get_or_create_business_entity(
                        business_entity, email_data
                    )
                    self.business_manager.record_user_business_interaction(
                        user=self.user,
                        business_entity=business_profile,
                        email_data=email_data,
                        category=category,
                        email_file_path=file_path,
                        template_id=similar_metadata.get('template_id') if similar_metadata else doc_id,
                        similarity_score=similarity_score
                    )
                    self.logger.info("Recorded business interaction", extra={
                        'user_email': self.user.email,
                        'user_id': self.user.id,
                        'business_name': business_profile.name,
                        'business_id': business_profile.id,
                        'category': category,
                        'similarity_score': similarity_score
                    })
                except Exception as e:
                    self.logger.error("Failed to record business interaction", extra={
                        'error': str(e),
                        'error_type': type(e).__name__,
                        'user_email': self.user.email,
                        'business_entity': business_entity
                    })
                    # Don't fail the entire processing if business recording fails

            if is_similar:
                processing_time = time.time() - start_time
                return ProcessingResult(
                    category=category,
                    business_entity=business_entity,
                    template=anonymized_template,
                    similarity_score=similarity_score,
                    matched_with=similar_metadata.get('filename', 'unknown'),
                    status=f"Similar template found (score: {similarity_score:.4f})",
                    processing_time=processing_time
                )
            
            # Store in vector DB
            filename = os.path.basename(file_path)
            self.vector_store.add_document(
                text=anonymized_template,
                metadata={
                    "category": category,
                    "business_entity": business_entity,
                    "filename": filename,
                    "subject": email_data.subject,
                    "from": email_data.from_field,
                    "date": email_data.date,
                    "template_id": doc_id
                },
                doc_id=doc_id
            )

            processing_time = time.time() - start_time
            return ProcessingResult(
                category=category,
                business_entity=business_entity,
                template=anonymized_template,
                status="Processed (new template)",
                processing_time=processing_time
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            error = handle_exception(self.logger, e, f"Processing email {file_path}")
            
            return ProcessingResult(
                category="unknown",
                business_entity="unknown",
                template="",
                status=f"Error: {str(error)}",
                processing_time=processing_time,
                error_message=str(error)
            )
    
    def process_emails(self) -> Dict[str, Any]:
        """Process all emails in the specified folder.
        
        Returns:
            Summary of processing results
        """
        self.logger.info(f"Processing all emails in folder: {self.email_folder}")
        
        # Check if email folder exists and is readable
        if not os.path.exists(self.email_folder):
            error_msg = f"Email folder '{self.email_folder}' does not exist"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg, stage="folder_validation")
        
        # Get list of email files
        try:
            email_files = [f for f in os.listdir(self.email_folder) if f.endswith('.eml')]
        except PermissionError as e:
            error_msg = f"Permission denied when accessing '{self.email_folder}'"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg, stage="folder_access")
        
        if not email_files:
            self.logger.warning(f"No .eml files found in '{self.email_folder}'")
            return {
                'total_files': 0,
                'processed': 0,
                'duplicates': 0,
                'similar': 0,
                'errors': 0,
                'results': []
            }
        
        self.logger.info(f"Found {len(email_files)} email files to process")
        
        # Track processing results
        results = {
            'total_files': len(email_files),
            'processed': 0,
            'duplicates': 0,
            'similar': 0,
            'errors': 0,
            'results': []
        }
        
        for filename in email_files:
            file_path = os.path.join(self.email_folder, filename)
            
            try:
                result = self.process_email(file_path)
                
                # Update counters
                if "Error" in result.status:
                    results['errors'] += 1
                elif "Duplicate" in result.status:
                    results['duplicates'] += 1
                elif "Similar" in result.status:
                    results['similar'] += 1
                elif "Processed" in result.status:
                    results['processed'] += 1
                
                # Store result summary
                results['results'].append({
                    'filename': filename,
                    'category': result.category,
                    'business_entity': result.business_entity,
                    'status': result.status,
                    'processing_time': result.processing_time,
                    'similarity_score': result.similarity_score
                })
                
                self.logger.info("Processed email file", extra={
                    'filename': filename,
                    'status': result.status,
                    'category': result.category,
                    'business_entity': result.business_entity,
                    'processing_time': result.processing_time,
                    'similarity_score': result.similarity_score
                })
                
            except Exception as e:
                results['errors'] += 1
                error_msg = f"Failed to process {filename}: {e}"
                self.logger.error(error_msg)
                
                results['results'].append({
                    'filename': filename,
                    'status': f"Error: {str(e)}",
                    'error': True
                })
        
        self.logger.info(f"Processing complete: {results['processed']} processed, "
                        f"{results['duplicates']} duplicates, {results['similar']} similar, "
                        f"{results['errors']} errors")
        
        return results
