import os
import argparse
from redis import Redis
from rq import Queue

from config.settings import get_config
from database.utils import get_db_manager, initialize_database
from email_queue.jobs import process_email_job  # Your RQ task function


def main():
    """Generate and enqueue email processing tasks into RQ queue."""
    parser = argparse.ArgumentParser(description='Generate email queue for processing')
    parser.add_argument('--user-email', type=str, required=True, help='Email address of the user')
    parser.add_argument('--emails-directory', type=str, default=None, help='Directory with .eml email files')
    parser.add_argument('--priority', type=int, default=0, help='Priority for queue items')

    args = parser.parse_args()

    # Load config and initialize database
    config = get_config()
    initialize_database()
    db = get_db_manager()

    # Resolve user
    user = db.get_or_create_user(args.user_email)
    print(f"Using user: {user.email} (ID: {user.id})")

    # Setup Redis and RQ queue
    redis_conn = Redis.from_url(config.redis.url)
    queue = Queue(name=config.redis.queue_name, connection=redis_conn)

    # Locate emails directory
    emails_directory = args.emails_directory or config.directories.email_folder
    if not os.path.exists(emails_directory):
        print(f"Error: Directory '{emails_directory}' does not exist.")
        return

    email_files = [f for f in os.listdir(emails_directory) if f.endswith('.eml')]
    if not email_files:
        print(f"No .eml files found in '{emails_directory}'")
        return

    print(f"Found {len(email_files)} email files to queue")

    # Enqueue items as raw dicts
    queued_count = 0
    for email_file in email_files:
        email_path = os.path.join(emails_directory, email_file)

        task_payload = {
            "user_id": user.id,
            "email_path": email_path,
            "priority": args.priority
        }

        try:
            queue.enqueue(process_email_job, user.id, email_path)
            print(f"✅ Queued: {email_file} for user {user.email}")
            queued_count += 1
        except Exception as e:
            print(f"❌ Failed to enqueue '{email_file}': {e}")

    print(f"📥 Successfully enqueued {queued_count} email(s)")
    print(f"📬 Total items in RQ queue '{queue.name}': {len(queue)}")


if __name__ == "__main__":
    main()
