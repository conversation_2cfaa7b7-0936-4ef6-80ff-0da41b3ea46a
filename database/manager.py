"""SQLAlchemy database manager for the email scanner application."""

import logging
import time
from typing import Optional, List, Dict, Any
from contextlib import contextmanager
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError, OperationalError
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from .models import (
    Base, User, BusinessEntity, EmailCategory, UserBusinessInteraction,
    BusinessInteractionSummary, BusinessPrivacyPolicy, PrivacyDataPractice
)
from config.settings import get_config, get_database_url
from utils.exceptions import DatabaseError, TemporaryDatabaseError

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages SQLAlchemy database connections and operations."""
    
    def __init__(self, database_url: str = None, **engine_kwargs):
        """Initialize database manager with connection parameters."""
        self.database_url = database_url or get_database_url()
        self.engine = None
        self.SessionLocal = None
        self._initialize_engine(**engine_kwargs)
        logger.info("DatabaseManager initialized successfully")
    
    def _initialize_engine(self, **engine_kwargs):
        """Initialize the SQLAlchemy engine."""
        try:
            config = get_config()
            db_config = config.database
            
            # Default engine configuration
            default_kwargs = {
                'poolclass': QueuePool,
                'pool_size': db_config.min_connections,
                'max_overflow': db_config.max_connections - db_config.min_connections,
                'pool_timeout': db_config.pool_timeout,
                'pool_recycle': db_config.pool_recycle,
                'pool_pre_ping': True,
                'echo': config.debug and config.log_level == 'DEBUG'
            }
            
            # Merge with provided kwargs
            default_kwargs.update(engine_kwargs)
            
            self.engine = create_engine(self.database_url, **default_kwargs)
            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
            
            logger.info("Database engine initialized", extra={
                'database_host': self.database_url.split('@')[1] if '@' in self.database_url else 'local',
                'pool_size': default_kwargs.get('pool_size', 5),
                'max_overflow': default_kwargs.get('max_overflow', 10)
            })

        except Exception as e:
            logger.error("Failed to initialize database engine", extra={
                'error': str(e),
                'error_type': type(e).__name__,
                'database_url': self.database_url.split('@')[1] if '@' in self.database_url else 'local'
            })
            raise DatabaseError(f"Failed to initialize database engine: {e}")
    
    @contextmanager
    def get_session(self) -> Session:
        """Get a database session with automatic cleanup."""
        session = self.SessionLocal()
        try:
            yield session
        except Exception as e:
            session.rollback()
            logger.error("Database session error", extra={
                'error': str(e),
                'error_type': type(e).__name__
            })
            raise
        finally:
            session.close()
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(OperationalError)
    )
    def test_connection(self) -> bool:
        """Test database connection with retry logic."""
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("Database connection test successful")
            return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            raise TemporaryDatabaseError(f"Database connection test failed: {e}")
    
    def create_tables(self):
        """Create all database tables."""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
            self._insert_default_categories()
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")
            raise DatabaseError(f"Failed to create database tables: {e}")
    
    def _insert_default_categories(self):
        """Insert default email categories."""
        default_categories = [
            ('business', 'Business-related communications'),
            ('social', 'Social media notifications and updates'),
            ('marketing', 'Marketing emails and newsletters'),
            ('personal', 'Personal communications'),
            ('shopping', 'Shopping and e-commerce related emails'),
            ('transaction', 'Transaction confirmations and receipts'),
            ('support', 'Customer support communications'),
            ('notification', 'System notifications and alerts')
        ]
        
        try:
            with self.get_session() as session:
                for name, description in default_categories:
                    existing = session.query(EmailCategory).filter(EmailCategory.name == name).first()
                    if not existing:
                        category = EmailCategory(name=name, description=description)
                        session.add(category)
                session.commit()
            logger.info("Default email categories inserted")
        except Exception as e:
            logger.error(f"Failed to insert default categories: {e}")
            # Don't raise here as this is not critical
    
    def verify_tables_exist(self) -> bool:
        """Verify that all required tables exist."""
        try:
            inspector = inspect(self.engine)
            existing_tables = inspector.get_table_names()
            
            required_tables = [
                'users', 'business_entities', 'email_categories',
                'user_business_interactions', 'business_interaction_summary',
                'email_processing_queue', 'business_privacy_policies',
                'privacy_data_practices'
            ]
            
            missing_tables = [table for table in required_tables if table not in existing_tables]
            
            if missing_tables:
                logger.warning(f"Missing tables: {missing_tables}")
                return False
            
            logger.info("All required tables exist")
            return True
            
        except Exception as e:
            logger.error(f"Error verifying tables: {e}")
            return False
    
    def wait_for_tables(self, max_wait_time: int = 300, check_interval: int = 10) -> bool:
        """Wait for all tables to be created with retry logic."""
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                if self.test_connection() and self.verify_tables_exist():
                    logger.info("All tables are ready")
                    return True
            except Exception as e:
                logger.warning(f"Tables not ready yet: {e}")
            
            logger.info(f"Waiting for tables... ({check_interval}s)")
            time.sleep(check_interval)
        
        logger.error(f"Tables not ready after {max_wait_time} seconds")
        return False
    
    # User management methods
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email address."""
        try:
            with self.get_session() as session:
                return User.get_by_email(session, email)
        except Exception as e:
            logger.error(f"Error getting user by email {email}: {e}")
            raise DatabaseError(f"Error getting user by email: {e}")
    
    def create_user(self, email: str, name: str = None) -> User:
        """Create a new user."""
        try:
            with self.get_session() as session:
                return User.create(session, email, name)
        except Exception as e:
            logger.error(f"Error creating user {email}: {e}")
            raise DatabaseError(f"Error creating user: {e}")
    
    def get_or_create_user(self, email: str, name: str = None) -> User:
        """Get existing user or create new one."""
        user = self.get_user_by_email(email)
        if not user:
            user = self.create_user(email, name)
        return user
    
    # Business entity management methods
    def get_business_entity_by_name(self, name: str) -> Optional[BusinessEntity]:
        """Get business entity by name."""
        try:
            with self.get_session() as session:
                return BusinessEntity.get_by_name(session, name)
        except Exception as e:
            logger.error(f"Error getting business entity by name {name}: {e}")
            raise DatabaseError(f"Error getting business entity by name: {e}")
    
    def create_business_entity(self, name: str, **kwargs) -> BusinessEntity:
        """Create a new business entity."""
        try:
            with self.get_session() as session:
                return BusinessEntity.create(session, name, **kwargs)
        except Exception as e:
            logger.error(f"Error creating business entity {name}: {e}")
            raise DatabaseError(f"Error creating business entity: {e}")
    
    def get_or_create_business_entity(self, name: str, **kwargs) -> BusinessEntity:
        """Get existing business entity or create new one."""
        business = self.get_business_entity_by_name(name)
        if not business:
            business = self.create_business_entity(name, **kwargs)
        return business
    
    # Email category methods
    def get_email_category_by_name(self, name: str) -> Optional[EmailCategory]:
        """Get email category by name."""
        try:
            with self.get_session() as session:
                return EmailCategory.get_by_name(session, name)
        except Exception as e:
            logger.error(f"Error getting email category by name {name}: {e}")
            raise DatabaseError(f"Error getting email category by name: {e}")
    
    def get_all_email_categories(self) -> List[EmailCategory]:
        """Get all email categories."""
        try:
            with self.get_session() as session:
                return EmailCategory.get_all(session)
        except Exception as e:
            logger.error(f"Error getting all email categories: {e}")
            raise DatabaseError(f"Error getting all email categories: {e}")
    
    # User-business interaction methods
    def create_user_business_interaction(self, **kwargs) -> UserBusinessInteraction:
        """Create a new user-business interaction record."""
        try:
            with self.get_session() as session:
                return UserBusinessInteraction.create(session, **kwargs)
        except Exception as e:
            logger.error(f"Error creating user-business interaction: {e}")
            raise DatabaseError(f"Error creating user-business interaction: {e}")
    
    def get_user_business_interactions(self, user_id: int, limit: int = 100) -> List[UserBusinessInteraction]:
        """Get recent user-business interactions."""
        try:
            with self.get_session() as session:
                return UserBusinessInteraction.get_user_interactions(session, user_id, limit)
        except Exception as e:
            logger.error(f"Error getting user interactions for user {user_id}: {e}")
            raise DatabaseError(f"Error getting user interactions: {e}")
    
    def get_business_interaction_summary(self, user_id: int, business_entity_id: int) -> Optional[BusinessInteractionSummary]:
        """Get business interaction summary for a user and business."""
        try:
            with self.get_session() as session:
                return BusinessInteractionSummary.get_or_create(session, user_id, business_entity_id)
        except Exception as e:
            logger.error(f"Error getting business interaction summary: {e}")
            raise DatabaseError(f"Error getting business interaction summary: {e}")
    
    # Privacy policy methods
    def get_business_privacy_policy(self, business_entity_id: int) -> Optional[BusinessPrivacyPolicy]:
        """Get privacy policy for a business entity."""
        try:
            with self.get_session() as session:
                return BusinessPrivacyPolicy.get_by_business_id(session, business_entity_id)
        except Exception as e:
            logger.error(f"Error getting privacy policy for business {business_entity_id}: {e}")
            raise DatabaseError(f"Error getting privacy policy: {e}")

    def create_business_privacy_policy(self, **kwargs) -> BusinessPrivacyPolicy:
        """Create a new business privacy policy record."""
        try:
            with self.get_session() as session:
                return BusinessPrivacyPolicy.create(session, **kwargs)
        except Exception as e:
            logger.error(f"Error creating privacy policy: {e}")
            raise DatabaseError(f"Error creating privacy policy: {e}")

    def update_business_privacy_policy(self, policy_id: int, **kwargs) -> Optional[BusinessPrivacyPolicy]:
        """Update a business privacy policy record."""
        try:
            with self.get_session() as session:
                policy = session.query(BusinessPrivacyPolicy).filter(BusinessPrivacyPolicy.id == policy_id).first()
                if policy:
                    for key, value in kwargs.items():
                        if hasattr(policy, key):
                            setattr(policy, key, value)
                    session.commit()
                    session.refresh(policy)
                    logger.info(f"Updated privacy policy {policy_id}")
                return policy
        except Exception as e:
            logger.error(f"Error updating privacy policy {policy_id}: {e}")
            raise DatabaseError(f"Error updating privacy policy: {e}")

    def create_privacy_data_practice(self, **kwargs) -> PrivacyDataPractice:
        """Create a new privacy data practice record."""
        try:
            with self.get_session() as session:
                return PrivacyDataPractice.create(session, **kwargs)
        except Exception as e:
            logger.error(f"Error creating data practice: {e}")
            raise DatabaseError(f"Error creating data practice: {e}")

    def get_privacy_data_practices(self, privacy_policy_id: int) -> List[PrivacyDataPractice]:
        """Get all data practices for a privacy policy."""
        try:
            with self.get_session() as session:
                return PrivacyDataPractice.get_by_policy_id(session, privacy_policy_id)
        except Exception as e:
            logger.error(f"Error getting data practices for policy {privacy_policy_id}: {e}")
            raise DatabaseError(f"Error getting data practices: {e}")

    def get_data_practices_by_category(self, category: str) -> List[PrivacyDataPractice]:
        """Get all data practices by category."""
        try:
            with self.get_session() as session:
                return PrivacyDataPractice.get_by_category(session, category)
        except Exception as e:
            logger.error(f"Error getting data practices for category {category}: {e}")
            raise DatabaseError(f"Error getting data practices by category: {e}")

    def close(self):
        """Close database connections."""
        if self.engine:
            self.engine.dispose()
            logger.info("Database connections closed")


# Global database manager instance
_db_manager = None


def get_db_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager


def reset_db_manager():
    """Reset the global database manager instance."""
    global _db_manager
    if _db_manager:
        _db_manager.close()
    _db_manager = None
