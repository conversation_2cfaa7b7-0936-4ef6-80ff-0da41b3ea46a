Extract the business entity or organization name from this email information:

From: {from_field}
Subject: {subject}

Instructions:
- Identify the business, company, or organization name
- Return only the clean business name without email domains
- If it's from an individual person, return "Individual"
- If it's a system/automated email, extract the service name
- Remove common suffixes like "Inc", "LLC", "Corp" for consistency
- If unclear, extract the domain name without extension

Business Name:
