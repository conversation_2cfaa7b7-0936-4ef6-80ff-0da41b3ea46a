#!/usr/bin/env python3
"""Test script to verify that the tools can be instantiated without errors."""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_tools_instantiation():
    """Test that all tools can be instantiated without errors."""
    try:
        from agents.tools import get_business_identification_tools
        
        print("Testing tool instantiation...")
        tools = get_business_identification_tools()
        
        print(f"✅ Successfully loaded {len(tools)} tools:")
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")
        
        # Test that each tool has the required attributes
        for tool in tools:
            assert hasattr(tool, 'name'), f"Tool {tool.__class__.__name__} missing 'name' attribute"
            assert hasattr(tool, 'description'), f"Tool {tool.__class__.__name__} missing 'description' attribute"
            assert hasattr(tool, 'args_schema'), f"Tool {tool.__class__.__name__} missing 'args_schema' attribute"
            assert hasattr(tool, '_run'), f"Tool {tool.__class__.__name__} missing '_run' method"
        
        print("✅ All tools have required attributes")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing tools: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_tools_instantiation()
    sys.exit(0 if success else 1)
