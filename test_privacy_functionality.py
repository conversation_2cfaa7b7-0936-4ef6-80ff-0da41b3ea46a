#!/usr/bin/env python3
"""Test script for privacy policy scraping functionality."""

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from config.settings import set_environment, get_config
from config.logging_config import setup_logging, get_logger
from database.utils import ensure_database_ready
from database.manager import get_db_manager
from business.manager import get_business_manager
from business.privacy_scraper import PrivacyPolicyScraper
from core.models import EmailData

logger = get_logger('test_privacy')

def test_privacy_scraper():
    """Test the privacy policy scraper functionality."""
    print("\n=== Testing Privacy Policy Scraper ===")
    
    scraper = PrivacyPolicyScraper()
    
    # Test finding privacy policy URL
    test_domains = [
        'google.com',
        'microsoft.com',
        'github.com'
    ]
    
    for domain in test_domains:
        print(f"\nTesting domain: {domain}")
        privacy_url = scraper.find_privacy_policy_url(domain)
        if privacy_url:
            print(f"  ✓ Found privacy policy: {privacy_url}")
            
            # Test scraping the privacy policy
            privacy_data = scraper.scrape_privacy_policy(privacy_url)
            if privacy_data:
                print(f"  ✓ Scraped privacy policy successfully")
                print(f"    Contact Email: {privacy_data.contact_info.email}")
                print(f"    Contact Phone: {privacy_data.contact_info.phone}")
                print(f"    Data Practices: {len(privacy_data.data_practices)}")
                for practice in privacy_data.data_practices[:3]:  # Show first 3
                    print(f"      - {practice.category}: {practice.description[:50]}...")
            else:
                print(f"  ✗ Failed to scrape privacy policy")
        else:
            print(f"  ✗ No privacy policy found")
    
    scraper.close()

def test_database_models():
    """Test the privacy policy database models."""
    print("\n=== Testing Database Models ===")
    
    try:
        # Ensure database is ready
        ensure_database_ready()
        
        db = get_db_manager()
        
        # Test creating a business entity
        business_entity = db.get_or_create_business_entity(
            name="Test Company",
            domain="testcompany.com",
            industry="Technology"
        )
        print(f"✓ Created/found business entity: {business_entity.name} (ID: {business_entity.id})")
        
        # Test creating privacy policy record
        privacy_policy = db.create_business_privacy_policy(
            business_entity_id=business_entity.id,
            privacy_policy_url="https://testcompany.com/privacy",
            scraping_status="success",
            contact_email="<EMAIL>",
            contact_phone="(*************"
        )
        print(f"✓ Created privacy policy record (ID: {privacy_policy.id})")
        
        # Test creating data practice
        data_practice = db.create_privacy_data_practice(
            privacy_policy_id=privacy_policy.id,
            category="personal_info",
            description="We collect personal information such as name and email address",
            third_party_sharing=False
        )
        print(f"✓ Created data practice record (ID: {data_practice.id})")
        
        # Test retrieving privacy policy
        retrieved_policy = db.get_business_privacy_policy(business_entity.id)
        if retrieved_policy:
            print(f"✓ Retrieved privacy policy: {retrieved_policy.privacy_policy_url}")
        
        # Test retrieving data practices
        practices = db.get_privacy_data_practices(privacy_policy.id)
        print(f"✓ Retrieved {len(practices)} data practices")
        
        print("✓ All database model tests passed")
        
    except Exception as e:
        print(f"✗ Database model test failed: {e}")

def test_business_manager_integration():
    """Test the business manager privacy policy integration."""
    print("\n=== Testing Business Manager Integration ===")
    
    try:
        # Get business manager with privacy scraping enabled
        business_manager = get_business_manager()
        
        # Create a test email data
        email_data = EmailData(
            subject="Welcome to GitHub",
            body="Thank you for joining GitHub...",
            from_field="<EMAIL>",
            to_field="<EMAIL>",
            date="2024-01-01"
        )
        
        # Test creating business entity (this should trigger privacy scraping)
        business_entity = business_manager.get_or_create_business_entity("GitHub", email_data)
        print(f"✓ Created/found business entity: {business_entity.name} (ID: {business_entity.id})")
        
        # Check if privacy policy was scraped
        privacy_info = business_manager.get_business_privacy_policy(business_entity.id)
        if privacy_info:
            print(f"✓ Privacy policy found:")
            print(f"    URL: {privacy_info['privacy_policy_url']}")
            print(f"    Status: {privacy_info['scraping_status']}")
            print(f"    Contact Email: {privacy_info['contact_info']['email']}")
            print(f"    Data Practices: {len(privacy_info['data_practices'])}")
        else:
            print("ℹ No privacy policy found (may be expected for test)")
        
        # Test manual privacy policy scraping
        print("\nTesting manual privacy policy scraping...")
        try:
            result = business_manager.scrape_privacy_policy_manually(business_entity.id)
            if result:
                print(f"✓ Manual scraping completed: {result.scraping_status}")
            else:
                print("ℹ Manual scraping returned no result")
        except Exception as e:
            print(f"ℹ Manual scraping failed (expected for test): {e}")
        
        business_manager.close()
        print("✓ Business manager integration test completed")
        
    except Exception as e:
        print(f"✗ Business manager integration test failed: {e}")

def test_cli_commands():
    """Test CLI commands for privacy policy functionality."""
    print("\n=== Testing CLI Commands ===")
    
    try:
        from cli import EmailScannerCLI
        
        cli = EmailScannerCLI()
        
        # Get a business entity to test with
        db = get_db_manager()
        with db.get_session() as session:
            from database.models import BusinessEntity
            business_entity = session.query(BusinessEntity).first()
            
            if business_entity:
                print(f"Testing with business entity: {business_entity.name} (ID: {business_entity.id})")
                
                # This would normally be called via command line, but we can test the logic
                print("✓ CLI privacy policy commands are available")
                print("  - Use: python run_cli.py --user-email <EMAIL> privacy-policy <business_id>")
                print("  - Use: python run_cli.py --user-email <EMAIL> scrape-privacy <business_id>")
            else:
                print("ℹ No business entities found for CLI testing")
        
    except Exception as e:
        print(f"✗ CLI command test failed: {e}")

def main():
    """Main test function."""
    print("Privacy Policy Functionality Test Suite")
    print("=" * 50)
    
    try:
        # Set environment
        set_environment('local')
        
        # Setup logging
        config = get_config()
        setup_logging(config.log_level, None, config.environment)
        
        # Run tests
        test_privacy_scraper()
        test_database_models()
        test_business_manager_integration()
        test_cli_commands()
        
        print("\n" + "=" * 50)
        print("✓ All tests completed successfully!")
        print("\nNext steps:")
        print("1. Initialize database: python init_db.py --environment local")
        print("2. Test CLI: python run_cli.py --user-email <EMAIL> status")
        print("3. Process emails: python run_cli.py --user-email <EMAIL> process-all")
        print("4. Check privacy policies: python run_cli.py --user-email <EMAIL> privacy-policy <business_id>")
        
        return 0
        
    except Exception as e:
        print(f"\n✗ Test suite failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
