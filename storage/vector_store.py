"""Vector database storage for email templates."""
import tempfile
import logging
from typing import Dict, Any, Optional, Tuple
from tenacity import retry, stop_after_attempt, wait_exponential

from langchain_chroma import Chroma
from langchain_core.embeddings import Embeddings
import chromadb

from config.settings import get_config
from config.logging_config import LoggerMixin, log_execution_time
from utils.exceptions import VectorStoreError, handle_exception

logger = logging.getLogger(__name__)

class VectorStore(LoggerMixin):
    """Vector database storage for email templates."""

    def __init__(self, 
                 embeddings: Embeddings, 
                 chromadb_host: str = None, 
                 chromadb_port: int = None,
                 collection_name: str = None):
        """Initialize the vector store with ChromaDB HTTP client.

        Args:
            embeddings: The embeddings function to use
            chromadb_host: ChromaDB server host (uses config if not provided)
            chromadb_port: ChromaDB server port (uses config if not provided)
            collection_name: Collection name (uses config if not provided)
        """
        config = get_config()
        
        self.chromadb_host = chromadb_host or config.chromadb.host
        self.chromadb_port = chromadb_port or config.chromadb.port
        self.collection_name = collection_name or config.chromadb.collection_name
        self.embeddings = embeddings
        self.vectorstore = None
        self.is_fallback = False
        
        self._initialize_vectorstore()

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def _initialize_vectorstore(self):
        """Initialize the vector store with retry logic."""
        try:
            self.logger.info(f"Connecting to ChromaDB at http://{self.chromadb_host}:{self.chromadb_port}")
            
            # Create ChromaDB HTTP client
            chroma_client = chromadb.HttpClient(
                host=self.chromadb_host,
                port=self.chromadb_port
            )
            
            # Test connection
            chroma_client.heartbeat()
            
            # Initialize ChromaDB through LangChain with HTTP client
            self.vectorstore = Chroma(
                client=chroma_client,
                embedding_function=self.embeddings,
                collection_name=self.collection_name
            )
            
            # Verify collection access
            collection_count = self._get_collection_count()
            self.logger.info(f"Connected to ChromaDB collection '{self.collection_name}' with {collection_count} documents")
            
        except Exception as e:
            self.logger.warning(f"Error connecting to ChromaDB at {self.chromadb_host}:{self.chromadb_port}: {e}")
            self._initialize_fallback()

    def _initialize_fallback(self):
        """Initialize fallback local ChromaDB instance."""
        try:
            self.logger.warning("Falling back to local ChromaDB instance...")
            
            # Fallback to local file-based ChromaDB if HTTP connection fails
            temp_dir = tempfile.mkdtemp()
            self.logger.info(f"Using temporary directory for local ChromaDB: {temp_dir}")
            
            self.vectorstore = Chroma(
                persist_directory=temp_dir,
                embedding_function=self.embeddings,
                collection_name=self.collection_name
            )
            
            self.is_fallback = True
            self.logger.warning("Using fallback local ChromaDB instance")
            
        except Exception as e:
            error = VectorStoreError(
                f"Failed to initialize both remote and fallback ChromaDB: {e}",
                operation="initialization"
            )
            self.logger.error(str(error))
            raise error

    def _get_collection_count(self) -> int:
        """Get the number of documents in the collection."""
        try:
            if hasattr(self.vectorstore, '_collection'):
                return self.vectorstore._collection.count()
            return 0
        except Exception as e:
            self.logger.warning(f"Could not get collection count: {e}")
            return 0

    @log_execution_time
    def check_duplicate(self, doc_id: str) -> bool:
        """Check if a document ID already exists in the collection.
        
        Args:
            doc_id: Document ID to check
            
        Returns:
            True if document exists, False otherwise
            
        Raises:
            VectorStoreError: If the operation fails
        """
        try:
            if not self.vectorstore:
                raise VectorStoreError("VectorStore not initialized", operation="check_duplicate")
            
            existing_docs = self.vectorstore._collection.get(ids=[doc_id])
            exists = existing_docs and len(existing_docs['ids']) > 0
            
            self.logger.debug(f"Document {doc_id} exists: {exists}")
            return exists
            
        except Exception as e:
            error = handle_exception(self.logger, e, f"Checking duplicate for doc_id {doc_id}")
            if isinstance(error, VectorStoreError):
                raise error
            raise VectorStoreError(f"Error checking duplicate: {e}", operation="check_duplicate")

    @log_execution_time
    def find_similar(self, 
                    text: str, 
                    threshold: float = None, 
                    k: int = 1) -> Tuple[bool, Optional[Dict[str, Any]], Optional[float]]:
        """Find similar documents in the vector store.
        
        Args:
            text: Text to find similar documents for
            threshold: Similarity threshold (uses config default if not provided)
            k: Number of similar documents to retrieve
            
        Returns:
            Tuple containing:
            - Boolean indicating if a similar document was found
            - Metadata of the similar document (if found)
            - Similarity score (if found)
            
        Raises:
            VectorStoreError: If the operation fails
        """
        try:
            if not self.vectorstore:
                raise VectorStoreError("VectorStore not initialized", operation="find_similar")
            
            if threshold is None:
                config = get_config()
                threshold = config.processing.similarity_threshold
            
            self.logger.debug(f"Finding similar documents with threshold {threshold}")
            
            results = self.vectorstore.similarity_search_with_score(text, k=k)
            
            if not results:
                self.logger.debug("No similar documents found")
                return False, None, None
            
            # Check if the most similar document meets the threshold
            similar_doc, similarity_score = results[0]
            
            # Note: ChromaDB returns distance, lower is more similar
            # We need to convert to similarity score (higher is more similar)
            similarity_score = 1.0 - similarity_score
            
            if similarity_score < threshold:
                self.logger.debug(f"Most similar document score {similarity_score} below threshold {threshold}")
                return False, None, None
            
            self.logger.debug(f"Found similar document with score {similarity_score}")
            return True, similar_doc.metadata, similarity_score
            
        except Exception as e:
            error = handle_exception(self.logger, e, f"Finding similar documents for text (length: {len(text)})")
            if isinstance(error, VectorStoreError):
                raise error
            raise VectorStoreError(f"Error finding similar documents: {e}", operation="find_similar")

    @log_execution_time
    def add_document(self, 
                    text: str, 
                    metadata: Dict[str, Any], 
                    doc_id: str) -> None:
        """Add a document to the vector store.
        
        Args:
            text: Document text content
            metadata: Document metadata
            doc_id: Unique document identifier
            
        Raises:
            VectorStoreError: If the operation fails
        """
        try:
            if not self.vectorstore:
                raise VectorStoreError("VectorStore not initialized", operation="add_document")
            
            # Validate inputs
            if not text or not text.strip():
                raise VectorStoreError("Document text cannot be empty", operation="add_document")
            
            if not doc_id or not doc_id.strip():
                raise VectorStoreError("Document ID cannot be empty", operation="add_document")
            
            # Check if document already exists
            if self.check_duplicate(doc_id):
                self.logger.warning(f"Document {doc_id} already exists, skipping")
                return
            
            # Add metadata for tracking
            enhanced_metadata = metadata.copy()
            enhanced_metadata.update({
                'doc_id': doc_id,
                'text_length': len(text),
                'added_at': str(logger.info),  # Current timestamp
                'is_fallback': self.is_fallback
            })
            
            self.logger.debug(f"Adding document {doc_id} with {len(text)} characters")
            
            self.vectorstore.add_texts(
                texts=[text],
                metadatas=[enhanced_metadata],
                ids=[doc_id]
            )
            
            self.logger.info(f"Successfully added document {doc_id} to vector store")
            
        except Exception as e:
            error = handle_exception(self.logger, e, f"Adding document {doc_id}")
            if isinstance(error, VectorStoreError):
                raise error
            raise VectorStoreError(f"Error adding document: {e}", operation="add_document")

    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the vector store collection.
        
        Returns:
            Dictionary containing collection statistics
        """
        try:
            stats = {
                'collection_name': self.collection_name,
                'host': self.chromadb_host,
                'port': self.chromadb_port,
                'is_fallback': self.is_fallback,
                'document_count': self._get_collection_count(),
                'status': 'connected' if self.vectorstore else 'disconnected'
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting collection stats: {e}")
            return {
                'collection_name': self.collection_name,
                'status': 'error',
                'error': str(e)
            }

    def health_check(self) -> Dict[str, Any]:
        """Perform health check on the vector store.
        
        Returns:
            Dictionary containing health check results
        """
        health = {
            'status': 'healthy',
            'collection_accessible': False,
            'document_count': 0,
            'is_fallback': self.is_fallback,
            'errors': []
        }
        
        try:
            if not self.vectorstore:
                health['status'] = 'unhealthy'
                health['errors'].append('VectorStore not initialized')
                return health
            
            # Test collection access
            health['document_count'] = self._get_collection_count()
            health['collection_accessible'] = True
            
            # Test basic operations
            test_result = self.find_similar("test query", k=1)
            if test_result is not None:
                health['basic_operations'] = True
            
        except Exception as e:
            health['status'] = 'unhealthy'
            health['errors'].append(str(e))
            self.logger.error(f"Vector store health check failed: {e}")
        
        return health

    def close(self):
        """Close vector store connections."""
        try:
            if self.vectorstore and hasattr(self.vectorstore, '_client'):
                # Close ChromaDB client if possible
                pass  # ChromaDB client doesn't have explicit close method
            self.logger.info("Vector store connections closed")
        except Exception as e:
            self.logger.warning(f"Error closing vector store: {e}")


# Global vector store instance
_vector_store = None


def get_vector_store(embeddings: Embeddings = None) -> VectorStore:
    """Get the global vector store instance."""
    global _vector_store
    if _vector_store is None:
        if embeddings is None:
            raise VectorStoreError("Embeddings required for first initialization")
        _vector_store = VectorStore(embeddings)
    return _vector_store


def reset_vector_store():
    """Reset the global vector store instance."""
    global _vector_store
    if _vector_store:
        _vector_store.close()
    _vector_store = None
