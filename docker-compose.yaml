version: '3.8'
services:
  email-categorization-app:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - NUM_WORKERS=4
    volumes:
      - ./:/app
      - ./emails:/app/emails
      - ./data:/app/data
    depends_on:
      - ollama
      - redis
      - postgres
      - chromadb
    environment:
      - EMAIL_FOLDER=/app/emails
      - TEMPLATE_OUTPUT_DIR=/app/processed_email_templates
      - OLLAMA_BASE_URL=http://host.docker.internal:11434
      - REDIS_URL=redis://redis:6379
      - POSTGRES_DB=email_db
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=admin123
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - CHROMADB_HOST=chromadb
      - CHROMADB_PORT=8000
    # command: >
    #   sh -c "pip install --upgrade pip && pip install -r requirements.txt && python app.py"

  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/version"]
      interval: 10s
      timeout: 5s
      retries: 5
    entrypoint: ["sh", "-c"]
    command:
      - |
        ollama pull llama3 && \
        ollama pull nomic-embed-text && \
        exec ollama serve

  redis:
    image: redis:latest
    container_name: redis
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  postgres:
    image: postgres:latest
    container_name: postgres
    environment:
      POSTGRES_DB: email_db
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  adminer:
    image: adminer:latest
    container_name: adminer
    depends_on:
      - postgres
    ports:
      - "8080:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=postgres

  rq-dashboard:
    image: eoranged/rq-dashboard:latest
    container_name: rq-dashboard
    depends_on:
      - redis
    ports:
      - "9181:9181"
    environment:
      - REDIS_URL=redis://redis:6379
    command: ["--redis-url", "redis://redis:6379"]

  chromadb:
    image: chromadb/chroma
    container_name: chromadb
    ports:
      - "8000:8000"
    environment:
      - CHROMADB_DATABASE=demo_db
      - CHROMADB_DATABASE_PORT=5432
      - CHROMADB_DATABASE_USER=admin
      - CHROMADB_DATABASE_PASSWORD=admin123
      - CHROMADB_DATABASE_NAME=email_db


volumes:
  ollama_data:
  postgres_data: