"""Privacy policy scraping functionality for business entities."""

import re
import logging
import requests
from typing import Optional, Dict, List, Any, Tuple
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from dataclasses import dataclass
import time

from config.logging_config import LoggerMixin, log_execution_time
from utils.exceptions import BusinessManagerError, handle_exception

logger = logging.getLogger(__name__)

@dataclass
class ContactInfo:
    """Contact information extracted from privacy policy."""
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    website: Optional[str] = None
    company_name: Optional[str] = None

@dataclass
class DataCollectionPractice:
    """Data collection practice extracted from privacy policy."""
    category: str  # e.g., 'personal_info', 'cookies', 'analytics'
    description: str
    purpose: Optional[str] = None
    retention_period: Optional[str] = None
    third_party_sharing: bool = False

@dataclass
class PrivacyPolicyData:
    """Complete privacy policy data."""
    url: str
    contact_info: ContactInfo
    data_practices: List[DataCollectionPractice]
    last_updated: Optional[str] = None
    policy_text: Optional[str] = None

class PrivacyPolicyScraper(LoggerMixin):
    """Scrapes and extracts information from privacy policy pages."""
    
    def __init__(self, timeout: int = 30, max_retries: int = 3):
        """Initialize the privacy policy scraper.
        
        Args:
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
        """
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Common privacy policy URL patterns
        self.privacy_url_patterns = [
            '/privacy',
            '/privacy-policy',
            '/privacy.html',
            '/legal/privacy',
            '/privacy-notice',
            '/privacypolicy',
            '/terms-privacy',
            '/legal/privacy-policy'
        ]
        
        self.logger.info("PrivacyPolicyScraper initialized")
    
    def find_privacy_policy_url(self, base_url: str) -> Optional[str]:
        """Find privacy policy URL for a given website.
        
        Args:
            base_url: Base website URL
            
        Returns:
            Privacy policy URL if found, None otherwise
        """
        try:
            # Normalize base URL
            if not base_url.startswith(('http://', 'https://')):
                base_url = 'https://' + base_url
            
            parsed_url = urlparse(base_url)
            base_domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
            
            # Try common privacy policy URL patterns
            for pattern in self.privacy_url_patterns:
                privacy_url = urljoin(base_domain, pattern)
                
                try:
                    response = self.session.head(privacy_url, timeout=self.timeout)
                    if response.status_code == 200:
                        self.logger.info(f"Found privacy policy at: {privacy_url}")
                        return privacy_url
                except requests.RequestException:
                    continue
            
            # Try to find privacy policy link on the main page
            try:
                response = self.session.get(base_url, timeout=self.timeout)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Look for privacy policy links
                    privacy_links = soup.find_all('a', href=True)
                    for link in privacy_links:
                        href = link.get('href', '').lower()
                        text = link.get_text().lower()
                        
                        if any(keyword in href or keyword in text for keyword in 
                               ['privacy', 'privacypolicy', 'privacy-policy']):
                            privacy_url = urljoin(base_url, link['href'])
                            self.logger.info(f"Found privacy policy link: {privacy_url}")
                            return privacy_url
            
            except requests.RequestException as e:
                self.logger.warning(f"Could not access main page {base_url}: {e}")
            
            self.logger.warning(f"No privacy policy found for {base_url}")
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding privacy policy URL for {base_url}: {e}")
            return None
    
    @log_execution_time
    def scrape_privacy_policy(self, url: str) -> Optional[PrivacyPolicyData]:
        """Scrape privacy policy from a given URL.
        
        Args:
            url: Privacy policy URL
            
        Returns:
            PrivacyPolicyData if successful, None otherwise
        """
        try:
            self.logger.info(f"Scraping privacy policy from: {url}")
            
            # Fetch the page
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract text content
            policy_text = self._extract_text_content(soup)
            
            # Extract contact information
            contact_info = self._extract_contact_info(soup, policy_text)
            
            # Extract data collection practices
            data_practices = self._extract_data_practices(policy_text)
            
            # Extract last updated date
            last_updated = self._extract_last_updated(soup, policy_text)
            
            privacy_data = PrivacyPolicyData(
                url=url,
                contact_info=contact_info,
                data_practices=data_practices,
                last_updated=last_updated,
                policy_text=policy_text[:5000] if policy_text else None  # Limit text length
            )
            
            self.logger.info(f"Successfully scraped privacy policy from {url}")
            return privacy_data
            
        except Exception as e:
            error = handle_exception(self.logger, e, f"Scraping privacy policy from {url}")
            return None
    
    def _extract_text_content(self, soup: BeautifulSoup) -> str:
        """Extract clean text content from the page."""
        try:
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text content
            text = soup.get_text()
            
            # Clean up text
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
            
        except Exception as e:
            self.logger.warning(f"Error extracting text content: {e}")
            return ""
    
    def _extract_contact_info(self, soup: BeautifulSoup, text: str) -> ContactInfo:
        """Extract contact information from the privacy policy."""
        contact_info = ContactInfo()
        
        try:
            # Extract email addresses
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, text)
            if emails:
                # Prefer privacy/legal/contact emails
                for email in emails:
                    if any(keyword in email.lower() for keyword in ['privacy', 'legal', 'contact', 'support']):
                        contact_info.email = email
                        break
                else:
                    contact_info.email = emails[0]
            
            # Extract phone numbers
            phone_pattern = r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'
            phones = re.findall(phone_pattern, text)
            if phones:
                contact_info.phone = f"({phones[0][0]}) {phones[0][1]}-{phones[0][2]}"
            
            # Extract addresses (basic pattern)
            address_pattern = r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Drive|Dr|Lane|Ln|Way|Court|Ct|Place|Pl)[,\s]+[A-Za-z\s]+[,\s]+[A-Z]{2}\s+\d{5}'
            addresses = re.findall(address_pattern, text)
            if addresses:
                contact_info.address = addresses[0]
            
            # Extract website from meta tags or links
            website_meta = soup.find('meta', property='og:url')
            if website_meta:
                contact_info.website = website_meta.get('content')
            
            self.logger.debug(f"Extracted contact info: {contact_info}")
            return contact_info
            
        except Exception as e:
            self.logger.warning(f"Error extracting contact info: {e}")
            return contact_info
    
    def _extract_data_practices(self, text: str) -> List[DataCollectionPractice]:
        """Extract data collection practices from the privacy policy text."""
        practices = []
        
        try:
            text_lower = text.lower()
            
            # Define data collection categories and their indicators
            categories = {
                'personal_info': [
                    'personal information', 'personally identifiable', 'name', 'email address',
                    'phone number', 'address', 'date of birth', 'social security'
                ],
                'cookies': [
                    'cookies', 'tracking pixels', 'web beacons', 'local storage',
                    'session storage', 'browser storage'
                ],
                'analytics': [
                    'analytics', 'google analytics', 'usage data', 'website usage',
                    'user behavior', 'traffic analysis'
                ],
                'marketing': [
                    'marketing', 'advertising', 'promotional', 'newsletter',
                    'email marketing', 'targeted ads'
                ],
                'location': [
                    'location', 'geolocation', 'gps', 'ip address location',
                    'geographic information'
                ],
                'device_info': [
                    'device information', 'browser type', 'operating system',
                    'device id', 'mobile device'
                ]
            }
            
            # Extract practices for each category
            for category, keywords in categories.items():
                for keyword in keywords:
                    if keyword in text_lower:
                        # Find context around the keyword
                        start_idx = text_lower.find(keyword)
                        if start_idx != -1:
                            # Extract surrounding context (200 chars before and after)
                            context_start = max(0, start_idx - 200)
                            context_end = min(len(text), start_idx + len(keyword) + 200)
                            context = text[context_start:context_end].strip()
                            
                            practice = DataCollectionPractice(
                                category=category,
                                description=context,
                                third_party_sharing='third party' in context.lower() or 'share' in context.lower()
                            )
                            practices.append(practice)
                            break  # Only add one practice per category
            
            # Look for retention periods
            retention_patterns = [
                r'retain.*?(\d+\s+(?:days?|months?|years?))',
                r'keep.*?(\d+\s+(?:days?|months?|years?))',
                r'store.*?(\d+\s+(?:days?|months?|years?))'
            ]
            
            for pattern in retention_patterns:
                matches = re.findall(pattern, text_lower)
                if matches:
                    # Add retention info to existing practices
                    for practice in practices:
                        if not practice.retention_period:
                            practice.retention_period = matches[0]
                            break
            
            self.logger.info(f"Extracted {len(practices)} data collection practices")
            return practices
            
        except Exception as e:
            self.logger.warning(f"Error extracting data practices: {e}")
            return practices
    
    def _extract_last_updated(self, soup: BeautifulSoup, text: str) -> Optional[str]:
        """Extract last updated date from the privacy policy."""
        try:
            # Look for common date patterns
            date_patterns = [
                r'last updated:?\s*([A-Za-z]+ \d{1,2},? \d{4})',
                r'effective date:?\s*([A-Za-z]+ \d{1,2},? \d{4})',
                r'updated:?\s*([A-Za-z]+ \d{1,2},? \d{4})',
                r'(\d{1,2}/\d{1,2}/\d{4})',
                r'(\d{4}-\d{2}-\d{2})'
            ]
            
            for pattern in date_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    return matches[0]
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Error extracting last updated date: {e}")
            return None
    
    def scrape_business_privacy_policy(self, business_name: str, domain: str) -> Optional[PrivacyPolicyData]:
        """Scrape privacy policy for a business entity.
        
        Args:
            business_name: Name of the business
            domain: Business domain
            
        Returns:
            PrivacyPolicyData if successful, None otherwise
        """
        try:
            self.logger.info(f"Scraping privacy policy for business: {business_name} ({domain})")
            
            # Find privacy policy URL
            privacy_url = self.find_privacy_policy_url(domain)
            if not privacy_url:
                self.logger.warning(f"No privacy policy URL found for {domain}")
                return None
            
            # Scrape the privacy policy
            privacy_data = self.scrape_privacy_policy(privacy_url)
            if privacy_data and privacy_data.contact_info:
                # Update contact info with business name if not found
                if not privacy_data.contact_info.company_name:
                    privacy_data.contact_info.company_name = business_name
            
            return privacy_data
            
        except Exception as e:
            error = handle_exception(
                self.logger, 
                e, 
                f"Scraping privacy policy for business {business_name}"
            )
            return None
    
    def close(self):
        """Close the session."""
        if self.session:
            self.session.close()
            self.logger.info("Privacy policy scraper session closed")
