# Comprehensive Email Analysis Guide

This guide explains the new comprehensive email analysis functionality that returns structured JSON responses with business information and web search capabilities.

## Overview

The comprehensive email analysis feature combines email categorization, business entity extraction, and web search to provide complete business information in a structured JSON format.

## JSON Response Format

The analysis returns a JSON object with the following structure:

```json
{
  "email_category": "business",
  "business_entity_name": "Shopify",
  "business_entity_email": "<EMAIL>",
  "business_entity_website": "https://shopify.com",
  "business_entity_dpo_email": "<EMAIL>"
}
```

### Field Descriptions

- **email_category**: Category of the email (business, marketing, personal, shopping, transaction, support, notification, social)
- **business_entity_name**: Clean business/organization name extracted from the email
- **business_entity_email**: Main business contact email address
- **business_entity_website**: Official business website URL
- **business_entity_dpo_email**: Data Protection Officer (DPO) email address

If any information cannot be found or extracted, the field will contain an empty string `""`.

## Features

### 1. Email Categorization
Automatically categorizes emails into predefined categories:
- **business**: Business communications
- **marketing**: Promotional and advertising content
- **personal**: Personal communications
- **shopping**: E-commerce related emails
- **transaction**: Order confirmations, invoices, receipts
- **support**: Customer service communications
- **notification**: System alerts and notifications
- **social**: Social media and event-related emails

### 2. Business Entity Extraction
Extracts clean business names from email headers and content, removing:
- Email domains
- Common suffixes (Inc, LLC, Corp)
- System-generated prefixes

### 3. Web Search Integration
When business information is missing from the email, the system automatically:
- Searches the business website for contact information
- Looks for privacy policies and DPO contact details
- Extracts business email addresses from web pages
- Finds official website URLs

### 4. Privacy Policy Analysis
Automatically identifies and analyzes privacy policies to extract:
- Data Protection Officer contact information
- Privacy-related email addresses
- Contact information for data requests

## Usage

### Python API

```python
from core.processor import EmailProcessor
from core.models import EmailData

# Initialize processor
processor = EmailProcessor(user_email="<EMAIL>")

# Method 1: Analyze email data directly
email_data = EmailData(
    from_field="<EMAIL>",
    subject="Welcome to our service",
    body="Email content here..."
)

result = processor.analyze_email_comprehensive(email_data)
print(result)

# Method 2: Process email file with full pipeline
result = processor.process_email_comprehensive("path/to/email.eml")
print(result)
```

### Command Line Interface

```bash
# Process single email with comprehensive analysis
python cli.py --user-email <EMAIL> process-comprehensive emails/business_email.eml

# Traditional processing (for comparison)
python cli.py --user-email <EMAIL> process emails/business_email.eml
```

## Examples

### Business Email Analysis

Input email from `<EMAIL>`:
```json
{
  "email_category": "business",
  "business_entity_name": "Shopify",
  "business_entity_email": "<EMAIL>",
  "business_entity_website": "https://shopify.com",
  "business_entity_dpo_email": "<EMAIL>"
}
```

### Marketing Email Analysis

Input email from `<EMAIL>`:
```json
{
  "email_category": "marketing",
  "business_entity_name": "Amazon",
  "business_entity_email": "<EMAIL>",
  "business_entity_website": "https://amazon.com",
  "business_entity_dpo_email": "<EMAIL>"
}
```

### Personal Email Analysis

Input email from `<EMAIL>`:
```json
{
  "email_category": "personal",
  "business_entity_name": "Individual",
  "business_entity_email": "",
  "business_entity_website": "",
  "business_entity_dpo_email": ""
}
```

## Web Search Tools

The system includes several web search tools:

### 1. Web Scraping Tool
- Extracts contact information from business websites
- Identifies email addresses, phone numbers, and addresses
- Analyzes page content for business information

### 2. Domain Lookup Tool
- Performs DNS resolution and domain analysis
- Extracts domain-based business information
- Validates website accessibility

### 3. Business Search Tool
- Searches for business information using various APIs
- Constructs intelligent search queries
- Provides fallback search strategies

### 4. Privacy Policy Finder Tool
- Automatically locates privacy policy pages
- Extracts DPO contact information
- Identifies privacy-related email addresses

## Configuration

The comprehensive analysis uses the same configuration as the standard email processor:

```python
# Environment-based configuration
set_environment('local')  # or 'development', 'production'

# Custom configuration
processor = EmailProcessor(
    user_email="<EMAIL>",
    categories=["business", "marketing", "personal"]  # Custom categories
)
```

## Error Handling

The system includes robust error handling:

- **LLM Failures**: Falls back to manual extraction methods
- **Web Search Failures**: Continues processing with available information
- **Network Issues**: Gracefully handles timeouts and connection errors
- **Invalid JSON**: Parses and reconstructs valid JSON responses

## Performance

- **Processing Time**: Typically 2-5 seconds per email
- **Web Search**: Adds 1-3 seconds for missing information
- **Caching**: Results are cached to improve subsequent processing
- **Parallel Processing**: Supports batch processing of multiple emails

## Testing

Run the test suite to verify functionality:

```bash
# Run comprehensive analysis tests
python test_comprehensive_analysis.py

# Run usage examples
python example_comprehensive_usage.py
```

## Integration

The comprehensive analysis integrates with:
- **Vector Database**: Stores processed templates
- **PostgreSQL**: Records business interactions
- **Redis Queues**: Supports asynchronous processing
- **Business Manager**: Tracks business profiles and privacy policies

## Limitations

- Web search requires internet connectivity
- Some websites may block automated scraping
- DPO information may not be publicly available
- Processing time increases with web search operations

## Future Enhancements

- Integration with business directory APIs
- Enhanced privacy policy analysis
- Support for additional contact information types
- Improved business name normalization
- Real-time business information updates
