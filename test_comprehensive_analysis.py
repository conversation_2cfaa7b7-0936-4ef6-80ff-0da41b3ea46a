#!/usr/bin/env python3
"""Test script for comprehensive email analysis functionality."""

import json
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import set_environment, get_config
from config.logging_config import setup_logging
from core.processor import EmailProcessor
from core.models import EmailData

def test_comprehensive_analysis():
    """Test the comprehensive email analysis functionality."""
    
    # Set up environment
    set_environment('local')
    config = get_config()
    setup_logging(log_level='INFO', environment='local')
    
    print("Testing Comprehensive Email Analysis")
    print("=" * 50)
    
    # Test with a sample email file
    email_files = [
        "emails/business_meeting.eml",
        "emails/marketing_newsletter.eml", 
        "emails/personal_birthday.eml",
        "emails/shopping_order_confirmation.eml"
    ]
    
    # Initialize processor
    try:
        processor = EmailProcessor(user_email="<EMAIL>")
        print("✓ EmailProcessor initialized successfully")
    except Exception as e:
        print(f"✗ Failed to initialize EmailProcessor: {e}")
        return False
    
    # Test each email file
    for email_file in email_files:
        if not os.path.exists(email_file):
            print(f"⚠ Email file not found: {email_file}")
            continue
            
        print(f"\nProcessing: {email_file}")
        print("-" * 30)
        
        try:
            # Test comprehensive analysis
            result = processor.process_email_comprehensive(email_file)
            
            print("Comprehensive Analysis Result:")
            print(f"  📧 Email Category: {result.get('email_category', 'N/A')}")
            print(f"  🏢 Business Name: {result.get('business_entity_name', 'N/A')}")
            print(f"  📬 Business Email: {result.get('business_entity_email', 'N/A') or 'Not found'}")
            print(f"  🌐 Business Website: {result.get('business_entity_website', 'N/A') or 'Not found'}")
            print(f"  🛡️ DPO Email: {result.get('business_entity_dpo_email', 'N/A') or 'Not found'}")
            print(f"  ⏱️ Processing Time: {result.get('processing_time', 0):.2f}s")
            print(f"  📊 Status: {result.get('status', 'N/A')}")
            
            if result.get('similarity_score', 0) > 0:
                print(f"  🔍 Similarity Score: {result.get('similarity_score', 0):.2f}")
            
            # Test direct analysis method
            email_data = processor.extractor.extract_email_content(email_file)
            analysis_result = processor.analyze_email_comprehensive(email_data)
            
            print("\nDirect Analysis Result (JSON format):")
            print(json.dumps(analysis_result, indent=2))
            
        except Exception as e:
            print(f"✗ Error processing {email_file}: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("Comprehensive analysis test completed!")
    return True

def test_json_format():
    """Test that the analysis returns the correct JSON format."""
    
    print("\nTesting JSON Format Compliance")
    print("-" * 30)
    
    # Create a sample email data
    sample_email = EmailData(
        from_field="<EMAIL>",
        to_field="<EMAIL>", 
        subject="Welcome to our service",
        body="Thank you for signing up! Visit our website at https://example.com for more information.",
        date="2023-01-01"
    )
    
    try:
        processor = EmailProcessor(user_email="<EMAIL>")
        result = processor.analyze_email_comprehensive(sample_email)
        
        # Check required fields
        required_fields = [
            "email_category",
            "business_entity_name", 
            "business_entity_email",
            "business_entity_website",
            "business_entity_dpo_email"
        ]
        
        print("JSON Format Validation:")
        all_fields_present = True
        for field in required_fields:
            if field in result:
                print(f"  ✓ {field}: {result[field]}")
            else:
                print(f"  ✗ Missing field: {field}")
                all_fields_present = False
        
        if all_fields_present:
            print("\n✓ All required fields are present!")
            print("✓ JSON format is compliant!")
        else:
            print("\n✗ Some required fields are missing!")
            
        return all_fields_present
        
    except Exception as e:
        print(f"✗ Error testing JSON format: {e}")
        return False

if __name__ == "__main__":
    print("Email Scanner - Comprehensive Analysis Test")
    print("=" * 60)
    
    success = True
    
    # Test comprehensive analysis
    if not test_comprehensive_analysis():
        success = False
    
    # Test JSON format
    if not test_json_format():
        success = False
    
    if success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
