#!/usr/bin/env python3
"""Example usage of the comprehensive email analysis functionality."""

import json
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import set_environment
from config.logging_config import setup_logging
from core.processor import EmailProcessor
from core.models import EmailData

def example_comprehensive_analysis():
    """Example of using the comprehensive email analysis."""
    
    # Set up environment
    set_environment('local')
    setup_logging(log_level='INFO', environment='local')
    
    print("Email Scanner - Comprehensive Analysis Example")
    print("=" * 50)
    
    # Initialize processor
    processor = EmailProcessor(user_email="<EMAIL>")
    
    # Example 1: Analyze a business email
    print("\nExample 1: Business Email Analysis")
    print("-" * 30)
    
    business_email = EmailData(
        from_field="<EMAIL>",
        to_field="<EMAIL>",
        subject="Your Shopify store is ready!",
        body="""
        Congratulations! Your Shopify store has been successfully set up.
        
        You can now start selling online. Visit your admin panel at:
        https://admin.shopify.com
        
        If you need help, contact our support team or visit our help center.
        
        Best regards,
        The Shopify Team
        """,
        date="2023-12-01"
    )
    
    result = processor.analyze_email_comprehensive(business_email)
    print("Analysis Result:")
    print(json.dumps(result, indent=2))
    
    # Example 2: Analyze a marketing email
    print("\n\nExample 2: Marketing Email Analysis")
    print("-" * 30)
    
    marketing_email = EmailData(
        from_field="<EMAIL>",
        to_field="<EMAIL>", 
        subject="Black Friday Deals - Up to 70% Off!",
        body="""
        Don't miss out on our biggest sale of the year!
        
        🔥 Electronics - Up to 50% off
        🔥 Fashion - Up to 70% off
        🔥 Home & Garden - Up to 60% off
        
        Shop now at amazon.com
        
        This email was sent to you because you subscribed to Amazon deals.
        To unsubscribe, click here.
        """,
        date="2023-11-24"
    )
    
    result = processor.analyze_email_comprehensive(marketing_email)
    print("Analysis Result:")
    print(json.dumps(result, indent=2))
    
    # Example 3: Process an actual email file
    print("\n\nExample 3: Processing Email File")
    print("-" * 30)
    
    email_file = "emails/business_meeting.eml"
    if Path(email_file).exists():
        result = processor.process_email_comprehensive(email_file)
        
        print("Comprehensive Processing Result:")
        print(f"📧 Category: {result['email_category']}")
        print(f"🏢 Business: {result['business_entity_name']}")
        print(f"📬 Email: {result['business_entity_email'] or 'Not found'}")
        print(f"🌐 Website: {result['business_entity_website'] or 'Not found'}")
        print(f"🛡️ DPO: {result['business_entity_dpo_email'] or 'Not found'}")
        print(f"⏱️ Time: {result['processing_time']:.2f}s")
        print(f"📊 Status: {result['status']}")
    else:
        print(f"Email file not found: {email_file}")
    
    print("\n" + "=" * 50)
    print("Example completed!")

def example_cli_usage():
    """Show CLI usage examples."""
    
    print("\n\nCLI Usage Examples")
    print("=" * 50)
    
    print("1. Process a single email with comprehensive analysis:")
    print("   python cli.py --user-email <EMAIL> process-comprehensive emails/business_meeting.eml")
    
    print("\n2. Process a single email (traditional method):")
    print("   python cli.py --user-email <EMAIL> process emails/business_meeting.eml")
    
    print("\n3. Process all emails in the folder:")
    print("   python cli.py --user-email <EMAIL> process-all")
    
    print("\n4. Check system status:")
    print("   python cli.py --user-email <EMAIL> status")
    
    print("\n5. List business entities:")
    print("   python cli.py --user-email <EMAIL> list-entities --limit 10")

def example_json_format():
    """Show the expected JSON format."""
    
    print("\n\nExpected JSON Response Format")
    print("=" * 50)
    
    example_response = {
        "email_category": "business",
        "business_entity_name": "Shopify",
        "business_entity_email": "<EMAIL>",
        "business_entity_website": "https://shopify.com",
        "business_entity_dpo_email": "<EMAIL>"
    }
    
    print("The comprehensive analysis returns a JSON object with these fields:")
    print(json.dumps(example_response, indent=2))
    
    print("\nField Descriptions:")
    print("- email_category: Category of the email (business, marketing, personal, etc.)")
    print("- business_entity_name: Name of the business/organization")
    print("- business_entity_email: Main contact email for the business")
    print("- business_entity_website: Official website URL")
    print("- business_entity_dpo_email: Data Protection Officer email address")
    
    print("\nNote: If information cannot be found, the field will contain an empty string.")

if __name__ == "__main__":
    try:
        example_comprehensive_analysis()
        example_cli_usage()
        example_json_format()
        
        print("\n🎉 All examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
