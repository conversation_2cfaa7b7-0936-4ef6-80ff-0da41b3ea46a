"""Command-line interface for email categorization system."""
import sys
import argparse
import logging
from typing import List, Dict, Any

from config.settings import set_environment, get_config
from config.logging_config import setup_logging, get_logger
from database.utils import ensure_database_ready, get_database_status, check_database_health
from database.manager import get_db_manager
from business.manager import get_business_manager
from email_queue.manager import get_queue_manager
from core.processor import EmailProcessor
from utils.exceptions import EmailScannerError, handle_exception

logger = get_logger('cli')

class EmailScannerCLI:
    """Command-line interface for the email scanner."""
    
    def __init__(self, environment: str = None):
        """Initialize the CLI."""
        if environment:
            set_environment(environment)
        
        self.config = get_config()
        setup_logging(
            log_level=self.config.log_level,
            log_file=None,  # CLI uses console logging
            environment=self.config.environment
        )
        
        logger.info(f"Email Scanner CLI initialized for environment: {self.config.environment}")
    
    def ensure_system_ready(self):
        """Ensure the system is ready for operations."""
        try:
            ensure_database_ready()
            logger.info("System is ready")
        except Exception as e:
            logger.error(f"System not ready: {e}")
            raise EmailScannerError(f"System not ready: {e}")
    
    def process_single_email(self, user_email: str, email_file: str) -> Dict[str, Any]:
        """Process a single email file.
        
        Args:
            user_email: Email address of the user
            email_file: Path to the email file
            
        Returns:
            Processing result dictionary
        """
        try:
            self.ensure_system_ready()
            
            logger.info(f"Processing single email for user {user_email}: {email_file}")
            
            # Initialize processor
            processor = EmailProcessor(
                user_email=user_email,
                email_folder=self.config.directories.email_folder,
                template_output_dir=self.config.directories.template_output_dir
            )
            
            # Process the email
            result = processor.process_email(email_file)
            
            return {
                'status': 'success',
                'result': {
                    'category': result.category,
                    'business_entity': result.business_entity,
                    'status': result.status,
                    'similarity_score': result.similarity_score,
                    'matched_with': result.matched_with
                }
            }
            
        except Exception as e:
            error = handle_exception(logger, e, f"Processing email {email_file}")
            return {
                'status': 'error',
                'error': str(error),
                'error_type': type(error).__name__
            }
    
    def process_all_emails(self, user_email: str) -> Dict[str, Any]:
        """Process all emails in the email folder.
        
        Args:
            user_email: Email address of the user
            
        Returns:
            Processing summary
        """
        try:
            self.ensure_system_ready()
            
            logger.info(f"Processing all emails for user {user_email}")
            
            # Initialize processor
            processor = EmailProcessor(
                user_email=user_email,
                email_folder=self.config.directories.email_folder,
                template_output_dir=self.config.directories.template_output_dir
            )
            
            # Process all emails
            results = processor.process_emails()
            
            return {
                'status': 'success',
                'summary': results
            }
            
        except Exception as e:
            error = handle_exception(logger, e, f"Processing all emails for user {user_email}")
            return {
                'status': 'error',
                'error': str(error),
                'error_type': type(error).__name__
            }
    
    def list_email_categories(self) -> List[Dict[str, Any]]:
        """List all email categories."""
        try:
            self.ensure_system_ready()
            
            db = get_db_manager()
            categories = db.get_all_email_categories()
            
            return [
                {
                    'id': cat.id,
                    'name': cat.name,
                    'description': cat.description
                }
                for cat in categories
            ]
            
        except Exception as e:
            error = handle_exception(logger, e, "Listing email categories")
            raise error
    
    def list_business_entities(self, user_email: str, limit: int = 50) -> List[Dict[str, Any]]:
        """List business entities for a user.
        
        Args:
            user_email: Email address of the user
            limit: Maximum number of entities to return
            
        Returns:
            List of business entities with interaction statistics
        """
        try:
            self.ensure_system_ready()
            
            db = get_db_manager()
            business_manager = get_business_manager()
            
            # Get user
            user = db.get_user_by_email(user_email)
            if not user:
                raise EmailScannerError(f"User not found: {user_email}")
            
            # Get business relationships
            relationships = business_manager.get_user_business_relationships(user.id, limit)
            
            return relationships
            
        except Exception as e:
            error = handle_exception(logger, e, f"Listing business entities for user {user_email}")
            raise error
    
    def list_user_interactions(self, user_email: str, limit: int = 20) -> List[Dict[str, Any]]:
        """List recent user interactions.
        
        Args:
            user_email: Email address of the user
            limit: Maximum number of interactions to return
            
        Returns:
            List of recent interactions
        """
        try:
            self.ensure_system_ready()
            
            db = get_db_manager()
            
            # Get user
            user = db.get_user_by_email(user_email)
            if not user:
                raise EmailScannerError(f"User not found: {user_email}")
            
            # Get interactions
            interactions = db.get_user_business_interactions(user.id, limit)
            
            return [
                {
                    'id': interaction.id,
                    'business_entity_id': interaction.business_entity_id,
                    'email_subject': interaction.email_subject,
                    'email_from': interaction.email_from,
                    'interaction_date': interaction.interaction_date.isoformat() if interaction.interaction_date else None,
                    'processing_status': interaction.processing_status,
                    'similarity_score': interaction.similarity_score
                }
                for interaction in interactions
            ]
            
        except Exception as e:
            error = handle_exception(logger, e, f"Listing interactions for user {user_email}")
            raise error
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        try:
            status = {
                'environment': self.config.environment,
                'timestamp': logger.info
            }
            
            # Database status
            status['database'] = check_database_health()
            
            # Queue status
            try:
                queue_manager = get_queue_manager()
                status['queue'] = queue_manager.health_check()
            except Exception as e:
                status['queue'] = {'status': 'error', 'error': str(e)}
            
            # Overall status
            if (status['database']['status'] == 'healthy' and 
                status['queue']['status'] == 'healthy'):
                status['overall'] = 'healthy'
            else:
                status['overall'] = 'unhealthy'
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                'overall': 'error',
                'error': str(e)
            }
    
    def enqueue_emails(self, user_email: str, email_paths: List[str] = None, priority: int = 0) -> Dict[str, Any]:
        """Enqueue emails for processing.
        
        Args:
            user_email: Email address of the user
            email_paths: List of email file paths (if None, enqueue all emails in folder)
            priority: Priority for the jobs
            
        Returns:
            Enqueue result summary
        """
        try:
            self.ensure_system_ready()
            
            db = get_db_manager()
            queue_manager = get_queue_manager()
            
            # Get or create user
            user = db.get_or_create_user(user_email)
            
            # If no email paths provided, scan the email folder
            if email_paths is None:
                import os
                email_folder = self.config.directories.email_folder
                email_paths = [
                    os.path.join(email_folder, f)
                    for f in os.listdir(email_folder)
                    if f.endswith('.eml')
                ]
            
            # Enqueue emails
            jobs = []
            for email_path in email_paths:
                try:
                    job = queue_manager.enqueue_email_processing(
                        user_id=user.id,
                        email_path=email_path,
                        priority=priority
                    )
                    jobs.append(job.id)
                except Exception as e:
                    logger.error(f"Failed to enqueue {email_path}: {e}")
            
            return {
                'status': 'success',
                'user_id': user.id,
                'user_email': user.email,
                'total_emails': len(email_paths),
                'enqueued': len(jobs),
                'job_ids': jobs
            }
            
        except Exception as e:
            error = handle_exception(logger, e, f"Enqueuing emails for user {user_email}")
            return {
                'status': 'error',
                'error': str(error),
                'error_type': type(error).__name__
            }


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description='Email Categorization and Template Extraction CLI')
    
    # Global arguments
    parser.add_argument(
        '--environment', 
        type=str, 
        choices=['local', 'development', 'production'],
        help='Environment to run in'
    )
    parser.add_argument(
        '--user-email', 
        type=str, 
        required=True,
        help='Email address of the user'
    )
    
    # Subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Process single email
    process_parser = subparsers.add_parser('process', help='Process a single email file')
    process_parser.add_argument('email_file', help='Path to the email file')
    
    # Process all emails
    subparsers.add_parser('process-all', help='Process all emails in the folder')
    
    # List categories
    subparsers.add_parser('list-categories', help='List all email categories')
    
    # List business entities
    entities_parser = subparsers.add_parser('list-entities', help='List business entities')
    entities_parser.add_argument('--limit', type=int, default=50, help='Maximum number of entities')
    
    # List interactions
    interactions_parser = subparsers.add_parser('list-interactions', help='List recent interactions')
    interactions_parser.add_argument('--limit', type=int, default=20, help='Maximum number of interactions')
    
    # System status
    subparsers.add_parser('status', help='Show system status')
    
    # Enqueue emails
    enqueue_parser = subparsers.add_parser('enqueue', help='Enqueue emails for processing')
    enqueue_parser.add_argument('--priority', type=int, default=0, help='Job priority')
    enqueue_parser.add_argument('email_files', nargs='*', help='Email files to enqueue (all if none specified)')

    # Privacy policy commands
    privacy_parser = subparsers.add_parser('privacy-policy', help='Get privacy policy for a business')
    privacy_parser.add_argument('business_id', type=int, help='Business entity ID')

    scrape_privacy_parser = subparsers.add_parser('scrape-privacy', help='Manually scrape privacy policy')
    scrape_privacy_parser.add_argument('business_id', type=int, help='Business entity ID')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        # Initialize CLI
        cli = EmailScannerCLI(environment=args.environment)
        
        # Execute command
        if args.command == 'process':
            result = cli.process_single_email(args.user_email, args.email_file)
            print(f"Processing result: {result}")
            
        elif args.command == 'process-all':
            result = cli.process_all_emails(args.user_email)
            print(f"Processing summary: {result}")
            
        elif args.command == 'list-categories':
            categories = cli.list_email_categories()
            print("Email Categories:")
            for cat in categories:
                print(f"  {cat['name']}: {cat['description']}")
                
        elif args.command == 'list-entities':
            entities = cli.list_business_entities(args.user_email, args.limit)
            print(f"Business Entities for {args.user_email}:")
            for entity in entities:
                print(f"  {entity['business_name']} ({entity['interaction_count']} interactions)")
                
        elif args.command == 'list-interactions':
            interactions = cli.list_user_interactions(args.user_email, args.limit)
            print(f"Recent Interactions for {args.user_email}:")
            for interaction in interactions:
                print(f"  {interaction['interaction_date']}: {interaction['email_subject']}")
                
        elif args.command == 'status':
            status = cli.get_system_status()
            print("System Status:")
            print(f"  Overall: {status['overall']}")
            print(f"  Database: {status['database']['status']}")
            print(f"  Queue: {status['queue']['status']}")
            
        elif args.command == 'enqueue':
            email_files = args.email_files if args.email_files else None
            result = cli.enqueue_emails(args.user_email, email_files, args.priority)
            print(f"Enqueue result: {result}")

        elif args.command == 'privacy-policy':
            business_manager = get_business_manager()
            try:
                policy_info = business_manager.get_business_privacy_policy(args.business_id)
                if policy_info:
                    print(f"Privacy Policy for Business ID {args.business_id}:")
                    print(f"  URL: {policy_info['privacy_policy_url']}")
                    print(f"  Status: {policy_info['scraping_status']}")
                    print(f"  Last Scraped: {policy_info['last_scraped_at']}")
                    print(f"  Contact Email: {policy_info['contact_info']['email']}")
                    print(f"  Contact Phone: {policy_info['contact_info']['phone']}")
                    print(f"  Data Practices: {len(policy_info['data_practices'])} found")
                    for practice in policy_info['data_practices']:
                        print(f"    - {practice['category']}: {practice['description'][:100]}...")
                else:
                    print(f"No privacy policy found for business ID {args.business_id}")
            except Exception as e:
                print(f"Error getting privacy policy: {e}")

        elif args.command == 'scrape-privacy':
            business_manager = get_business_manager()
            try:
                print(f"Manually scraping privacy policy for business ID {args.business_id}...")
                result = business_manager.scrape_privacy_policy_manually(args.business_id)
                if result:
                    print(f"Privacy policy scraping completed: {result.scraping_status}")
                    if result.scraping_error:
                        print(f"Error: {result.scraping_error}")
                else:
                    print("Privacy policy scraping failed")
            except Exception as e:
                print(f"Error scraping privacy policy: {e}")
        
        return 0
        
    except EmailScannerError as e:
        logger.error(f"Email scanner error: {e}")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
